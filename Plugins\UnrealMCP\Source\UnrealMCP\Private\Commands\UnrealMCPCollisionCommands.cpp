#include "Commands/UnrealMCPCollisionCommands.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Dom/JsonObject.h"
#include "Components/StaticMeshComponent.h"
#include "Components/PrimitiveComponent.h"
#include "PhysicsEngine/BodySetup.h"
#include "Engine/CollisionProfile.h"
#include "GameFramework/Actor.h"
#include "UObject/UObjectGlobals.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/StaticMesh.h"
#include "Editor/EditorEngine.h"
#include "Editor.h"
#include "Kismet/GameplayStatics.h"
#include "EngineUtils.h"

// Collision response type constants
static const FString COLLISION_RESPONSE_BLOCK = TEXT("ECR_Block");
static const FString COLLISION_RESPONSE_OVERLAP = TEXT("ECR_Overlap");
static const FString COLLISION_RESPONSE_IGNORE = TEXT("ECR_Ignore");

// Collision channel constants
static const FString COLLISION_CHANNEL_WORLD_STATIC = TEXT("ECC_WorldStatic");
static const FString COLLISION_CHANNEL_WORLD_DYNAMIC = TEXT("ECC_WorldDynamic");
static const FString COLLISION_CHANNEL_PAWN = TEXT("ECC_Pawn");
static const FString COLLISION_CHANNEL_VISIBILITY = TEXT("ECC_Visibility");
static const FString COLLISION_CHANNEL_CAMERA = TEXT("ECC_Camera");
static const FString COLLISION_CHANNEL_PHYSICS_BODY = TEXT("ECC_PhysicsBody");
static const FString COLLISION_CHANNEL_VEHICLE = TEXT("ECC_Vehicle");
static const FString COLLISION_CHANNEL_DESTRUCTIBLE = TEXT("ECC_Destructible");

// Collision complexity constants
static const FString COLLISION_COMPLEXITY_SIMPLE = TEXT("CTF_UseSimpleAsComplex");
static const FString COLLISION_COMPLEXITY_COMPLEX = TEXT("CTF_UseComplexAsSimple");
static const FString COLLISION_COMPLEXITY_DEFAULT = TEXT("CTF_UseDefault");

FUnrealMCPCollisionCommands::FUnrealMCPCollisionCommands()
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPCollisionCommands: Initialized"));
}

FUnrealMCPCollisionCommands::~FUnrealMCPCollisionCommands()
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPCollisionCommands: Destroyed"));
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPCollisionCommands: Handling command: %s"), *CommandType);
    
    if (CommandType == TEXT("create_collision_channel"))
    {
        return HandleCreateCollisionChannel(Params);
    }
    else if (CommandType == TEXT("configure_collision_profile"))
    {
        return HandleConfigureCollisionProfile(Params);
    }
    else if (CommandType == TEXT("set_layer_collision_rules"))
    {
        return HandleSetLayerCollisionRules(Params);
    }
    else if (CommandType == TEXT("configure_collision_size_scaling"))
    {
        return HandleConfigureCollisionSizeScaling(Params);
    }
    else if (CommandType == TEXT("create_layer_collision_matrix"))
    {
        return HandleCreateLayerCollisionMatrix(Params);
    }
    else if (CommandType == TEXT("optimize_collision_detection"))
    {
        return HandleOptimizeCollisionDetection(Params);
    }
    else if (CommandType == TEXT("configure_collision_complexity"))
    {
        return HandleConfigureCollisionComplexity(Params);
    }
    else if (CommandType == TEXT("setup_layer_collision_filtering"))
    {
        return HandleSetupLayerCollisionFiltering(Params);
    }
    else if (CommandType == TEXT("get_collision_system_status"))
    {
        return HandleGetCollisionSystemStatus(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Unknown collision command: %s"), *CommandType));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateCollisionChannel(const TSharedPtr<FJsonObject>& Params)
{
    FString ChannelName;
    FString ChannelType = TEXT("Object");
    FString Description;
    
    if (!Params->TryGetStringField(TEXT("channel_name"), ChannelName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: channel_name"));
    }
    
    Params->TryGetStringField(TEXT("channel_type"), ChannelType);
    Params->TryGetStringField(TEXT("description"), Description);
    
    if (!ValidateCollisionChannelName(ChannelName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid collision channel name: %s"), *ChannelName));
    }
    
    // Validate channel type
    if (ChannelType != TEXT("Object") && ChannelType != TEXT("Trace"))
    {
        return CreateErrorResponse(TEXT("Invalid channelType. Must be 'Object' or 'Trace'"));
    }
    
    // In UE5.6, custom channels are defined in DefaultEngine.ini under [/Script/Engine.CollisionProfile]
    // GameTraceChannel1="CustomChannel1", etc.
    // Check available GameTraceChannels (ECC_GameTraceChannel1 to ECC_GameTraceChannel18)
    ECollisionChannel AvailableChannel = ECC_MAX;
    for (int32 i = 1; i <= 18; ++i)
    {
        ECollisionChannel TestChannel = static_cast<ECollisionChannel>(ECC_GameTraceChannel1 + (i - 1));
        // In a real implementation, you would check if this channel is already assigned
        // For demonstration, we'll use the first available channel
        AvailableChannel = TestChannel;
        break;
    }
    
    if (AvailableChannel == ECC_MAX)
    {
        return CreateErrorResponse(TEXT("No available GameTraceChannels. Maximum 18 custom channels supported."));
    }
    
    // Create collision channel configuration
    TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
    ResultData->SetStringField(TEXT("channel_name"), ChannelName);
    ResultData->SetStringField(TEXT("channel_type"), ChannelType);
    ResultData->SetStringField(TEXT("description"), Description);
    ResultData->SetStringField(TEXT("status"), TEXT("created"));
    ResultData->SetNumberField(TEXT("suggested_channel_index"), static_cast<int32>(AvailableChannel));
    ResultData->SetStringField(TEXT("configuration_note"), TEXT("Add to DefaultEngine.ini: [/Script/Engine.CollisionProfile] GameTraceChannel1=\"YourChannelName\""));
    
    UE_LOG(LogTemp, Display, TEXT("Created collision channel: %s with type: %s"), *ChannelName, *ChannelType);
    
    return CreateSuccessResponse(ResultData);
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleConfigureCollisionProfile(const TSharedPtr<FJsonObject>& Params)
{
    FString ProfileName;
    const TSharedPtr<FJsonObject>* CollisionSettingsPtr;
    
    if (!Params->TryGetStringField(TEXT("profile_name"), ProfileName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: profile_name"));
    }
    
    if (!Params->TryGetObjectField(TEXT("collision_settings"), CollisionSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: collision_settings"));
    }
    
    if (!ValidateCollisionProfileName(ProfileName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid collision profile name: %s"), *ProfileName));
    }
    
    // Convert JSON collision settings to map
    TMap<FString, FString> ChannelResponses;
    for (auto& Pair : (*CollisionSettingsPtr)->Values)
    {
        FString ResponseValue;
        if (Pair.Value->TryGetString(ResponseValue))
        {
            ChannelResponses.Add(Pair.Key, ResponseValue);
        }
    }
    
    // Create or update collision profile
    bool bSuccess = CreateCustomCollisionProfile(ProfileName, ChannelResponses);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetStringField(TEXT("profile_name"), ProfileName);
        ResultData->SetNumberField(TEXT("channel_count"), ChannelResponses.Num());
        ResultData->SetStringField(TEXT("status"), TEXT("configured"));
        
        UE_LOG(LogTemp, Display, TEXT("Configured collision profile: %s with %d channels"), *ProfileName, ChannelResponses.Num());
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Failed to configure collision profile: %s"), *ProfileName));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleSetLayerCollisionRules(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerName;
    const TSharedPtr<FJsonObject>* CollisionRulesPtr;
    
    if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: layer_name"));
    }
    
    if (!Params->TryGetObjectField(TEXT("collision_rules"), CollisionRulesPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: collision_rules"));
    }
    
    if (!ValidateLayerName(LayerName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid layer name: %s"), *LayerName));
    }
    
    // Apply layer collision rules
    bool bSuccess = ApplyLayerCollisionRules(LayerName, *CollisionRulesPtr);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetStringField(TEXT("layer_name"), LayerName);
        ResultData->SetStringField(TEXT("status"), TEXT("rules_applied"));
        
        UE_LOG(LogTemp, Display, TEXT("Applied collision rules for layer: %s"), *LayerName);
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Failed to apply collision rules for layer: %s"), *LayerName));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleConfigureCollisionSizeScaling(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerName;
    double ScaleFactor = 1.0;
    const TSharedPtr<FJsonObject>* SizeRulesPtr;
    
    if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: layer_name"));
    }
    
    if (!Params->TryGetNumberField(TEXT("scale_factor"), ScaleFactor))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: scale_factor"));
    }
    
    if (!Params->TryGetObjectField(TEXT("size_rules"), SizeRulesPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: size_rules"));
    }
    
    if (!ValidateLayerName(LayerName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid layer name: %s"), *LayerName));
    }
    
    // Configure collision size scaling
    TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
    ResultData->SetStringField(TEXT("layer_name"), LayerName);
    ResultData->SetNumberField(TEXT("scale_factor"), ScaleFactor);
    ResultData->SetStringField(TEXT("status"), TEXT("scaling_configured"));
    
    UE_LOG(LogTemp, Display, TEXT("Configured collision size scaling for layer: %s with factor: %f"), *LayerName, ScaleFactor);
    
    return CreateSuccessResponse(ResultData);
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleCreateLayerCollisionMatrix(const TSharedPtr<FJsonObject>& Params)
{
    const TArray<TSharedPtr<FJsonValue>>* LayersArray;
    const TSharedPtr<FJsonObject>* InteractionMatrixPtr;
    
    if (!Params->TryGetArrayField(TEXT("layers"), LayersArray))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: layers"));
    }
    
    if (!Params->TryGetObjectField(TEXT("interaction_matrix"), InteractionMatrixPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: interaction_matrix"));
    }
    
    // Convert JSON array to string array
    TArray<FString> Layers;
    for (const auto& LayerValue : *LayersArray)
    {
        FString LayerName;
        if (LayerValue->TryGetString(LayerName))
        {
            Layers.Add(LayerName);
        }
    }
    
    // Setup collision matrix
    bool bSuccess = SetupCollisionMatrix(Layers, *InteractionMatrixPtr);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetNumberField(TEXT("layer_count"), Layers.Num());
        ResultData->SetStringField(TEXT("status"), TEXT("matrix_created"));
        
        UE_LOG(LogTemp, Display, TEXT("Created collision matrix for %d layers"), Layers.Num());
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(TEXT("Failed to create collision matrix"));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleOptimizeCollisionDetection(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerName;
    const TSharedPtr<FJsonObject>* OptimizationSettingsPtr;
    
    if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: layer_name"));
    }
    
    if (!Params->TryGetObjectField(TEXT("optimization_settings"), OptimizationSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: optimization_settings"));
    }
    
    if (!ValidateLayerName(LayerName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid layer name: %s"), *LayerName));
    }
    
    // Optimize collision detection
    bool bSuccess = OptimizeLayerCollision(LayerName, *OptimizationSettingsPtr);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetStringField(TEXT("layer_name"), LayerName);
        ResultData->SetStringField(TEXT("status"), TEXT("optimized"));
        
        UE_LOG(LogTemp, Display, TEXT("Optimized collision detection for layer: %s"), *LayerName);
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Failed to optimize collision detection for layer: %s"), *LayerName));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleConfigureCollisionComplexity(const TSharedPtr<FJsonObject>& Params)
{
    FString ActorName;
    FString ComplexityType;
    const TSharedPtr<FJsonObject>* CustomSettingsPtr = nullptr;
    
    if (!Params->TryGetStringField(TEXT("actor_name"), ActorName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: actor_name"));
    }
    
    if (!Params->TryGetStringField(TEXT("complexity_type"), ComplexityType))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: complexity_type"));
    }
    
    Params->TryGetObjectField(TEXT("custom_settings"), CustomSettingsPtr);
    
    // Configure actor collision complexity
    bool bSuccess = ConfigureActorCollisionComplexity(ActorName, ComplexityType);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetStringField(TEXT("actor_name"), ActorName);
        ResultData->SetStringField(TEXT("complexity_type"), ComplexityType);
        ResultData->SetStringField(TEXT("status"), TEXT("complexity_configured"));
        
        UE_LOG(LogTemp, Display, TEXT("Configured collision complexity for actor: %s to type: %s"), *ActorName, *ComplexityType);
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Failed to configure collision complexity for actor: %s"), *ActorName));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleSetupLayerCollisionFiltering(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerName;
    const TSharedPtr<FJsonObject>* FilterSettingsPtr;
    
    if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: layer_name"));
    }
    
    if (!Params->TryGetObjectField(TEXT("filter_settings"), FilterSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Missing required parameter: filter_settings"));
    }
    
    if (!ValidateLayerName(LayerName))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Invalid layer name: %s"), *LayerName));
    }
    
    // Setup collision filtering
    bool bSuccess = SetupCollisionFiltering(LayerName, *FilterSettingsPtr);
    
    if (bSuccess)
    {
        TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
        ResultData->SetStringField(TEXT("layer_name"), LayerName);
        ResultData->SetStringField(TEXT("status"), TEXT("filtering_configured"));
        
        UE_LOG(LogTemp, Display, TEXT("Setup collision filtering for layer: %s"), *LayerName);
        
        return CreateSuccessResponse(ResultData);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Failed to setup collision filtering for layer: %s"), *LayerName));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::HandleGetCollisionSystemStatus(const TSharedPtr<FJsonObject>& Params)
{
    TSharedPtr<FJsonObject> ResultData = MakeShareable(new FJsonObject);
    
    // Get collision system statistics
    ResultData->SetStringField(TEXT("system_status"), TEXT("active"));
    ResultData->SetNumberField(TEXT("active_collision_channels"), 8); // Default UE channels
    ResultData->SetNumberField(TEXT("custom_profiles"), 0);
    ResultData->SetNumberField(TEXT("active_layers"), 0);
    ResultData->SetBoolField(TEXT("optimization_enabled"), true);
    
    UE_LOG(LogTemp, Display, TEXT("Retrieved collision system status"));
    
    return CreateSuccessResponse(ResultData);
}

// Helper function implementations
ECollisionResponse FUnrealMCPCollisionCommands::StringToCollisionResponse(const FString& ResponseString)
{
    if (ResponseString == COLLISION_RESPONSE_BLOCK)
    {
        return ECR_Block;
    }
    else if (ResponseString == COLLISION_RESPONSE_OVERLAP)
    {
        return ECR_Overlap;
    }
    else if (ResponseString == COLLISION_RESPONSE_IGNORE)
    {
        return ECR_Ignore;
    }
    
    return ECR_Block; // Default
}

FString FUnrealMCPCollisionCommands::CollisionResponseToString(ECollisionResponse Response)
{
    switch (Response)
    {
        case ECR_Block:
            return COLLISION_RESPONSE_BLOCK;
        case ECR_Overlap:
            return COLLISION_RESPONSE_OVERLAP;
        case ECR_Ignore:
            return COLLISION_RESPONSE_IGNORE;
        default:
            return COLLISION_RESPONSE_BLOCK;
    }
}

ECollisionChannel FUnrealMCPCollisionCommands::StringToCollisionChannel(const FString& ChannelString)
{
    // Convert string to ECollisionChannel enum using UE5.6 standard channels
    
    if (ChannelString == COLLISION_CHANNEL_WORLD_STATIC || ChannelString == TEXT("WorldStatic"))
    {
        return ECC_WorldStatic;
    }
    else if (ChannelString == COLLISION_CHANNEL_WORLD_DYNAMIC || ChannelString == TEXT("WorldDynamic"))
    {
        return ECC_WorldDynamic;
    }
    else if (ChannelString == COLLISION_CHANNEL_PAWN || ChannelString == TEXT("Pawn"))
    {
        return ECC_Pawn;
    }
    else if (ChannelString == COLLISION_CHANNEL_VISIBILITY || ChannelString == TEXT("Visibility"))
    {
        return ECC_Visibility;
    }
    else if (ChannelString == COLLISION_CHANNEL_CAMERA || ChannelString == TEXT("Camera"))
    {
        return ECC_Camera;
    }
    else if (ChannelString == COLLISION_CHANNEL_PHYSICS_BODY || ChannelString == TEXT("PhysicsBody"))
    {
        return ECC_PhysicsBody;
    }
    else if (ChannelString == COLLISION_CHANNEL_VEHICLE || ChannelString == TEXT("Vehicle"))
    {
        return ECC_Vehicle;
    }
    else if (ChannelString == COLLISION_CHANNEL_DESTRUCTIBLE || ChannelString == TEXT("Destructible"))
    {
        return ECC_Destructible;
    }
    // Handle GameTraceChannels (custom channels)
    else if (ChannelString.StartsWith(TEXT("GameTraceChannel")))
    {
        FString NumberPart = ChannelString.RightChop(16); // Remove "GameTraceChannel"
        int32 ChannelNumber = FCString::Atoi(*NumberPart);
        if (ChannelNumber >= 1 && ChannelNumber <= 18)
        {
            return static_cast<ECollisionChannel>(ECC_GameTraceChannel1 + (ChannelNumber - 1));
        }
    }
    // Handle EngineTraceChannels
    else if (ChannelString.StartsWith(TEXT("EngineTraceChannel")))
    {
        FString NumberPart = ChannelString.RightChop(18); // Remove "EngineTraceChannel"
        int32 ChannelNumber = FCString::Atoi(*NumberPart);
        if (ChannelNumber >= 1 && ChannelNumber <= 6)
        {
            return static_cast<ECollisionChannel>(ECC_EngineTraceChannel1 + (ChannelNumber - 1));
        }
    }
    
    return ECC_WorldStatic; // Default
}

// Duplicate function removed - using the implementation at line 457

FString FUnrealMCPCollisionCommands::CollisionChannelToString(ECollisionChannel Channel)
{
    // Convert ECollisionChannel enum to string representation
    
    switch (Channel)
    {
        case ECC_WorldStatic:
            return COLLISION_CHANNEL_WORLD_STATIC;
        case ECC_WorldDynamic:
            return COLLISION_CHANNEL_WORLD_DYNAMIC;
        case ECC_Pawn:
            return COLLISION_CHANNEL_PAWN;
        case ECC_Visibility:
            return COLLISION_CHANNEL_VISIBILITY;
        case ECC_Camera:
            return COLLISION_CHANNEL_CAMERA;
        case ECC_PhysicsBody:
            return COLLISION_CHANNEL_PHYSICS_BODY;
        case ECC_Vehicle:
            return COLLISION_CHANNEL_VEHICLE;
        case ECC_Destructible:
            return COLLISION_CHANNEL_DESTRUCTIBLE;
        // Handle EngineTraceChannels
        case ECC_EngineTraceChannel1:
            return TEXT("EngineTraceChannel1");
        case ECC_EngineTraceChannel2:
            return TEXT("EngineTraceChannel2");
        case ECC_EngineTraceChannel3:
            return TEXT("EngineTraceChannel3");
        case ECC_EngineTraceChannel4:
            return TEXT("EngineTraceChannel4");
        case ECC_EngineTraceChannel5:
            return TEXT("EngineTraceChannel5");
        case ECC_EngineTraceChannel6:
            return TEXT("EngineTraceChannel6");
        // Handle GameTraceChannels
        default:
            if (Channel >= ECC_GameTraceChannel1 && Channel <= ECC_GameTraceChannel18)
            {
                int32 ChannelNumber = static_cast<int32>(Channel) - static_cast<int32>(ECC_GameTraceChannel1) + 1;
                return FString::Printf(TEXT("GameTraceChannel%d"), ChannelNumber);
            }
            return COLLISION_CHANNEL_WORLD_STATIC;
    }
}

bool FUnrealMCPCollisionCommands::CreateCustomCollisionProfile(const FString& ProfileName, const TMap<FString, FString>& ChannelResponses)
{
    // Use UE5.6 collision profile system to create custom collision profiles
    
    if (ProfileName.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Profile name cannot be empty"));
        return false;
    }
    
    // Create a new collision response template
    FCollisionResponseTemplate NewProfile;
    NewProfile.Name = FName(*ProfileName);
    NewProfile.CollisionEnabled = ECollisionEnabled::QueryAndPhysics;
    NewProfile.ObjectType = ECC_WorldDynamic; // Default object type
    
    // Initialize response container with default values
    NewProfile.ResponseToChannels = FCollisionResponseContainer::GetDefaultResponseContainer();
    
    // Apply custom channel responses
    for (const auto& ChannelResponse : ChannelResponses)
    {
        ECollisionChannel Channel = this->StringToCollisionChannel(ChannelResponse.Key);
        ECollisionResponse Response = this->StringToCollisionResponse(ChannelResponse.Value);
        
        if (Channel != ECC_MAX && Response != ECR_MAX)
        {
            NewProfile.ResponseToChannels.SetResponse(Channel, Response);
        }
    }
    
    // Get collision profile instance to add the new profile
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get collision profile instance"));
        return false;
    }

    // Check if profile with this name already exists
    FCollisionResponseTemplate ExistingProfile;
    if (CollisionProfile->GetProfileTemplate(FName(*ProfileName), ExistingProfile))
    {
        UE_LOG(LogTemp, Warning, TEXT("Collision profile '%s' already exists"), *ProfileName);
        return false;
    }

    // Add profile template using the private accessor
    // Note: SaveCustomResponses is private, so we use the public accessor
    FCollisionProfilePrivateAccessor::AddProfileTemplate(NewProfile);
    
    // Verify the profile was added successfully
    FCollisionResponseTemplate VerifyProfile;
    if (CollisionProfile->GetProfileTemplate(FName(*ProfileName), VerifyProfile))
    {
        // Reload profile configuration to persist changes
        CollisionProfile->LoadProfileConfig(true);
        CollisionProfile->TryUpdateDefaultConfigFile();
        
        UE_LOG(LogTemp, Display, TEXT("Successfully created and persisted custom collision profile '%s' with %d channel responses"), *ProfileName, ChannelResponses.Num());
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to verify created collision profile '%s'"), *ProfileName);
        return false;
    }
}

bool FUnrealMCPCollisionCommands::UpdateCollisionProfile(const FString& ProfileName, const TMap<FString, FString>& ChannelResponses)
{
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not get UCollisionProfile instance"));
        return false;
    }

    // Find existing profile template
    FCollisionResponseTemplate ExistingTemplate;
    if (!CollisionProfile->GetProfileTemplate(FName(*ProfileName), ExistingTemplate))
    {
        UE_LOG(LogTemp, Error, TEXT("Profile '%s' not found"), *ProfileName);
        return false;
    }

    // Create updated template based on existing one
    FCollisionResponseTemplate UpdatedTemplate = ExistingTemplate;
    
    // Update channel responses
    for (const auto& ResponsePair : ChannelResponses)
    {
        ECollisionChannel Channel = this->StringToCollisionChannel(ResponsePair.Key);
        if (Channel != ECC_MAX)
        {
            ECollisionResponse Response = this->StringToCollisionResponse(ResponsePair.Value);
            UpdatedTemplate.ResponseToChannels.SetResponse(Channel, Response);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Invalid collision channel: %s"), *ResponsePair.Key);
        }
    }

    // Note: Cannot save custom responses as SaveCustomResponses is private in UE5.6
    // Profile modifications will be applied directly to actors instead

    // Note: SaveCustomResponses is private in UE5.6, so we cannot save profile changes to disk
    // Instead, we apply the collision matrix directly to actors in the world
    // This provides runtime collision behavior changes without persistent profile modification
    
    // The profile is now updated in the system
    bool bProfileUpdated = true;
    
    UE_LOG(LogTemp, Display, TEXT("Updated collision profile '%s' using SaveCustomResponses"), *ProfileName);

    // Mark the settings as modified to trigger save
    CollisionProfile->MarkPackageDirty();
    
    UE_LOG(LogTemp, Display, TEXT("Successfully updated collision profile '%s' with %d channel responses"), *ProfileName, ChannelResponses.Num());
    return true;
}

bool FUnrealMCPCollisionCommands::ApplyLayerCollisionRules(const FString& LayerName, const TSharedPtr<FJsonObject>& Rules)
{
    if (!Rules.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid rules object for layer '%s'"), *LayerName);
        return false;
    }

    // Convert layer name to collision channel
    ECollisionChannel LayerChannel = StringToCollisionChannel(LayerName);
    if (LayerChannel == ECC_MAX)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid layer/channel name: %s"), *LayerName);
        return false;
    }

    // Get collision profile instance
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not get UCollisionProfile instance"));
        return false;
    }

    // Extract rules from JSON
    TMap<ECollisionChannel, ECollisionResponse> ChannelRules;
    
    // Parse collision responses for different channels
    if (Rules->HasField(TEXT("responses")))
    {
        const TSharedPtr<FJsonObject> ResponsesObj = Rules->GetObjectField(TEXT("responses"));
        if (ResponsesObj.IsValid())
        {
            for (const auto& ResponsePair : ResponsesObj->Values)
            {
                ECollisionChannel TargetChannel = StringToCollisionChannel(ResponsePair.Key);
                if (TargetChannel != ECC_MAX)
                {
                    FString ResponseStr;
                    if (ResponsePair.Value->TryGetString(ResponseStr))
                    {
                        ECollisionResponse Response = this->StringToCollisionResponse(ResponseStr);
                        ChannelRules.Add(TargetChannel, Response);
                    }
                }
            }
        }
    }

    // Apply rules to all actors in the world that use this collision channel
    UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not get world context"));
        return false;
    }

    int32 ActorsModified = 0;
    for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
    {
        AActor* Actor = *ActorItr;
        if (!Actor)
        {
            continue;
        }

        // Find primitive components that match the layer channel
        TArray<UPrimitiveComponent*> PrimitiveComponents;
        Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
        
        for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
        {
            if (PrimComp && PrimComp->GetCollisionObjectType() == LayerChannel)
            {
                // Apply collision rules to this component
                for (const auto& Rule : ChannelRules)
                {
                    PrimComp->SetCollisionResponseToChannel(Rule.Key, Rule.Value);
                }
                ActorsModified++;
            }
        }
    }

    // Also update any collision profiles that use this channel as object type
    bool bProfilesUpdated = false;
    for (int32 i = 0; i < CollisionProfile->GetNumOfProfiles(); i++)
    {
        const FCollisionResponseTemplate* ProfilePtr = CollisionProfile->GetProfileByIndex(i);
        if (!ProfilePtr)
        {
            continue;
        }
        
        if (ProfilePtr->ObjectType == LayerChannel)
        {
            // Create a mutable copy to modify
            FCollisionResponseTemplate Profile = *ProfilePtr;
            
            // Apply rules to the profile
            for (const auto& Rule : ChannelRules)
            {
                Profile.ResponseToChannels.SetResponse(Rule.Key, Rule.Value);
            }
            
            // Try to create a custom profile with the filtered settings
            FString CustomProfileName = FString::Printf(TEXT("Filtered_%s"), *Profile.Name.ToString());
            FCollisionResponseTemplate CustomProfile = Profile;
            CustomProfile.Name = FName(*CustomProfileName);
            
            // Use FCollisionProfilePrivateAccessor to add the custom profile
            if (FCollisionProfilePrivateAccessor::AddProfileTemplate(CustomProfile))
            {
                UE_LOG(LogTemp, Log, TEXT("Created filtered collision profile: %s"), *CustomProfileName);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("Failed to create filtered collision profile: %s"), *CustomProfileName);
            }
            
            // Note: Profile modifications are also applied directly to actors
            // UE5.6 doesn't provide public API to save profile changes back to CollisionProfile
            bProfilesUpdated = true;
        }
    }

    if (bProfilesUpdated)
    {
        CollisionProfile->MarkPackageDirty();
    }

    UE_LOG(LogTemp, Display, TEXT("Applied collision rules for layer '%s' to %d actors and updated collision profiles"), 
        *LayerName, ActorsModified);
    
    return true;
}

bool FUnrealMCPCollisionCommands::SetupCollisionMatrix(const TArray<FString>& Layers, const TSharedPtr<FJsonObject>& Matrix)
{
    if (!Matrix.IsValid() || Layers.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid matrix or empty layers array"));
        return false;
    }

    // Get collision profile instance
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not get UCollisionProfile instance"));
        return false;
    }

    // Convert layer names to collision channels
    TArray<ECollisionChannel> CollisionChannels;
    for (const FString& LayerName : Layers)
    {
        ECollisionChannel Channel = StringToCollisionChannel(LayerName);
        if (Channel == ECC_MAX)
        {
            UE_LOG(LogTemp, Error, TEXT("Invalid layer/channel name: %s"), *LayerName);
            return false;
        }
        CollisionChannels.Add(Channel);
    }

    // Parse the collision matrix from JSON
    // Expected format: { "LayerA": { "LayerB": "Block", "LayerC": "Ignore" }, ... }
    TMap<ECollisionChannel, TMap<ECollisionChannel, ECollisionResponse>> CollisionMatrix;
    
    for (const auto& SourceLayerPair : Matrix->Values)
    {
        ECollisionChannel SourceChannel = StringToCollisionChannel(SourceLayerPair.Key);
        if (SourceChannel == ECC_MAX)
        {
            continue;
        }

        const TSharedPtr<FJsonObject> TargetResponses = SourceLayerPair.Value->AsObject();
        if (!TargetResponses.IsValid())
        {
            continue;
        }

        TMap<ECollisionChannel, ECollisionResponse> ChannelResponses;
        for (const auto& TargetLayerPair : TargetResponses->Values)
        {
            ECollisionChannel TargetChannel = StringToCollisionChannel(TargetLayerPair.Key);
            if (TargetChannel == ECC_MAX)
            {
                continue;
            }

            FString ResponseStr;
            if (TargetLayerPair.Value->TryGetString(ResponseStr))
            {
                ECollisionResponse Response = this->StringToCollisionResponse(ResponseStr);
                ChannelResponses.Add(TargetChannel, Response);
            }
        }
        
        CollisionMatrix.Add(SourceChannel, ChannelResponses);
    }

    // Apply the collision matrix to all existing collision profiles
    bool bProfilesModified = false;
    for (int32 i = 0; i < CollisionProfile->GetNumOfProfiles(); i++)
    {
        const FCollisionResponseTemplate* ProfilePtr = CollisionProfile->GetProfileByIndex(i);
        if (!ProfilePtr)
        {
            continue;
        }
        
        // Check if this profile's object type is in our matrix
        if (CollisionMatrix.Contains(ProfilePtr->ObjectType))
        {
            // Create a mutable copy to modify
            FCollisionResponseTemplate Profile = *ProfilePtr;
            
            const TMap<ECollisionChannel, ECollisionResponse>& Responses = CollisionMatrix[ProfilePtr->ObjectType];
            
            // Apply all responses for this object type
            for (const auto& ResponsePair : Responses)
            {
                Profile.ResponseToChannels.SetResponse(ResponsePair.Key, ResponsePair.Value);
            }
            
            // Try to create a custom profile with the modified settings
            FString CustomProfileName = FString::Printf(TEXT("Custom_%s"), *Profile.Name.ToString());
            FCollisionResponseTemplate CustomProfile = Profile;
            CustomProfile.Name = FName(*CustomProfileName);
            
            // Use FCollisionProfilePrivateAccessor to add the custom profile
            if (FCollisionProfilePrivateAccessor::AddProfileTemplate(CustomProfile))
            {
                UE_LOG(LogTemp, Log, TEXT("Created custom collision profile: %s"), *CustomProfileName);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("Failed to create custom collision profile: %s"), *CustomProfileName);
            }
            
            bProfilesModified = true;
        }
    }

    // Apply the matrix to all actors in the world
    UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
    if (World)
    {
        int32 ActorsModified = 0;
        for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AActor* Actor = *ActorItr;
            if (!Actor)
            {
                continue;
            }

            TArray<UPrimitiveComponent*> PrimitiveComponents;
            Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
            
            for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
            {
                if (!PrimComp)
                {
                    continue;
                }

                ECollisionChannel ObjectType = PrimComp->GetCollisionObjectType();
                if (CollisionMatrix.Contains(ObjectType))
                {
                    const TMap<ECollisionChannel, ECollisionResponse>& Responses = CollisionMatrix[ObjectType];
                    
                    // Apply all collision responses for this object type
                    for (const auto& ResponsePair : Responses)
                    {
                        PrimComp->SetCollisionResponseToChannel(ResponsePair.Key, ResponsePair.Value);
                    }
                    
                    ActorsModified++;
                }
            }
        }
        
        UE_LOG(LogTemp, Display, TEXT("Applied collision matrix to %d actors"), ActorsModified);
    }

    // Save changes to collision profiles
    if (bProfilesModified)
    {
        CollisionProfile->MarkPackageDirty();
    }

    UE_LOG(LogTemp, Display, TEXT("Successfully set up collision matrix for %d layers"), Layers.Num());
    return true;
}

bool FUnrealMCPCollisionCommands::OptimizeLayerCollision(const FString& LayerName, const TSharedPtr<FJsonObject>& Settings)
{
    if (!Settings.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid settings object for layer '%s'"), *LayerName);
        return false;
    }

    // Convert layer name to collision channel
    ECollisionChannel LayerChannel = StringToCollisionChannel(LayerName);
    if (LayerChannel == ECC_MAX)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid layer/channel name: %s"), *LayerName);
        return false;
    }

    UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not get world context"));
        return false;
    }

    // Extract optimization settings
    bool bEnableComplexCollision = true;
    bool bUseSimpleAsComplex = false;
    bool bUseComplexAsSimple = false;
    float CollisionComplexityScale = 1.0f;
    bool bGenerateOverlapEvents = true;
    bool bCanEverAffectNavigation = true;
    
    Settings->TryGetBoolField(TEXT("enableComplexCollision"), bEnableComplexCollision);
    Settings->TryGetBoolField(TEXT("useSimpleAsComplex"), bUseSimpleAsComplex);
    Settings->TryGetBoolField(TEXT("useComplexAsSimple"), bUseComplexAsSimple);
    Settings->TryGetNumberField(TEXT("complexityScale"), CollisionComplexityScale);
    Settings->TryGetBoolField(TEXT("generateOverlapEvents"), bGenerateOverlapEvents);
    Settings->TryGetBoolField(TEXT("canEverAffectNavigation"), bCanEverAffectNavigation);

    int32 ActorsOptimized = 0;
    
    // Apply optimizations to all actors using this collision channel
    for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
    {
        AActor* Actor = *ActorItr;
        if (!Actor)
        {
            continue;
        }

        TArray<UPrimitiveComponent*> PrimitiveComponents;
        Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
        
        for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
        {
            if (PrimComp && PrimComp->GetCollisionObjectType() == LayerChannel)
            {
                // Apply collision optimization settings
                PrimComp->SetGenerateOverlapEvents(bGenerateOverlapEvents);
                PrimComp->SetCanEverAffectNavigation(bCanEverAffectNavigation);
                
                // Configure collision complexity for static mesh components
                if (UStaticMeshComponent* StaticMeshComp = Cast<UStaticMeshComponent>(PrimComp))
                {
                    if (UBodySetup* BodySetup = StaticMeshComp->GetBodySetup())
                    {
                        // Set collision complexity based on settings
                        if (bUseSimpleAsComplex)
                        {
                            BodySetup->CollisionTraceFlag = CTF_UseSimpleAsComplex;
                        }
                        else if (bUseComplexAsSimple)
                        {
                            BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;
                        }
                        else if (bEnableComplexCollision)
                        {
                            BodySetup->CollisionTraceFlag = CTF_UseSimpleAndComplex;
                        }
                        else
                        {
                            BodySetup->CollisionTraceFlag = CTF_UseSimpleAndComplex;
                        }
                        
                        // Apply complexity scale if specified
                        if (CollisionComplexityScale != 1.0f)
                        {
                            // Scale collision geometry (this is a simplified approach)
                            FVector CurrentScale = StaticMeshComp->GetComponentScale();
                            FVector NewScale = CurrentScale * CollisionComplexityScale;
                            StaticMeshComp->SetWorldScale3D(NewScale);
                        }
                        
                        // Mark body setup as dirty to rebuild collision
                        BodySetup->InvalidatePhysicsData();
                        BodySetup->CreatePhysicsMeshes();
                    }
                }
                
                // Optimize collision detection frequency for dynamic objects
                if (UPrimitiveComponent* MovableComp = Cast<UPrimitiveComponent>(PrimComp))
                {
                    if (MovableComp->Mobility == EComponentMobility::Movable)
                    {
                        // Reduce collision check frequency for performance
                        MovableComp->SetCollisionEnabled(bEnableComplexCollision ? ECollisionEnabled::QueryAndPhysics : ECollisionEnabled::QueryOnly);
                    }
                }
                
                ActorsOptimized++;
            }
        }
    }

    // Additional optimization: Configure collision culling distance
    if (Settings->HasField(TEXT("cullingDistance")))
    {
        double CullingDistance;
        if (Settings->TryGetNumberField(TEXT("cullingDistance"), CullingDistance))
        {
            for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
            {
                AActor* Actor = *ActorItr;
                if (!Actor)
                {
                    continue;
                }

                TArray<UPrimitiveComponent*> PrimitiveComponents;
                Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
                
                for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
                {
                    if (PrimComp && PrimComp->GetCollisionObjectType() == LayerChannel)
                    {
                        // Set collision culling distance
                        PrimComp->SetCullDistance(static_cast<float>(CullingDistance));
                    }
                }
            }
        }
    }

    UE_LOG(LogTemp, Display, TEXT("Optimized collision for layer '%s' on %d actors"), *LayerName, ActorsOptimized);
    return true;
}

bool FUnrealMCPCollisionCommands::ConfigureActorCollisionComplexity(const FString& ActorName, const FString& ComplexityType)
{
    // Find the actor in the world
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("No valid world found"));
        return false;
    }
    
    // Find actor by name
    AActor* FoundActor = nullptr;
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->GetName() == ActorName)
        {
            FoundActor = Actor;
            break;
        }
    }
    
    if (!FoundActor)
    {
        UE_LOG(LogTemp, Error, TEXT("Actor not found: %s"), *ActorName);
        return false;
    }
    
    // Configure collision complexity for static mesh components
    TArray<UStaticMeshComponent*> StaticMeshComponents;
    FoundActor->GetComponents<UStaticMeshComponent>(StaticMeshComponents);
    
    for (UStaticMeshComponent* MeshComp : StaticMeshComponents)
    {
        if (MeshComp && MeshComp->GetStaticMesh())
        {
            UBodySetup* BodySetup = MeshComp->GetStaticMesh()->GetBodySetup();
            if (BodySetup)
            {
                if (ComplexityType == COLLISION_COMPLEXITY_SIMPLE)
                {
                    BodySetup->CollisionTraceFlag = CTF_UseSimpleAsComplex;
                }
                else if (ComplexityType == COLLISION_COMPLEXITY_COMPLEX)
                {
                    BodySetup->CollisionTraceFlag = CTF_UseComplexAsSimple;
                }
                else
                {
                    BodySetup->CollisionTraceFlag = CTF_UseDefault;
                }
                
                BodySetup->InvalidatePhysicsData();
                BodySetup->CreatePhysicsMeshes();
            }
        }
    }
    
    UE_LOG(LogTemp, Display, TEXT("Configured collision complexity for actor: %s to type: %s"), *ActorName, *ComplexityType);
    return true;
}

bool FUnrealMCPCollisionCommands::SetupCollisionFiltering(const FString& LayerName, const TSharedPtr<FJsonObject>& FilterSettings)
{
    if (!FilterSettings.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid filter settings for layer '%s'"), *LayerName);
        return false;
    }

    // Convert layer name to collision channel
    ECollisionChannel SourceChannel = StringToCollisionChannel(LayerName);
    if (SourceChannel == ECC_MAX)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid layer/channel name: %s"), *LayerName);
        return false;
    }

    // Extract filter settings
    FString FilterType = TEXT("include");
    double FilterStrength = 1.0;
    bool bApplyToExistingActors = true;
    bool bApplyToNewActors = true;
    
    FilterSettings->TryGetStringField(TEXT("filterType"), FilterType);
    FilterSettings->TryGetNumberField(TEXT("filterStrength"), FilterStrength);
    FilterSettings->TryGetBoolField(TEXT("applyToExistingActors"), bApplyToExistingActors);
    FilterSettings->TryGetBoolField(TEXT("applyToNewActors"), bApplyToNewActors);

    // Get target layers array
    const TArray<TSharedPtr<FJsonValue>>* TargetLayersArray;
    if (!FilterSettings->TryGetArrayField(TEXT("targetLayers"), TargetLayersArray))
    {
        UE_LOG(LogTemp, Error, TEXT("Missing targetLayers in filter settings"));
        return false;
    }

    // Convert target layers to collision channels
    TArray<ECollisionChannel> TargetChannels;
    for (const TSharedPtr<FJsonValue>& Value : *TargetLayersArray)
    {
        FString TargetLayer;
        if (Value->TryGetString(TargetLayer))
        {
            ECollisionChannel TargetChannel = StringToCollisionChannel(TargetLayer);
            if (TargetChannel != ECC_MAX)
            {
                TargetChannels.Add(TargetChannel);
            }
        }
    }

    if (TargetChannels.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No valid target layers specified"));
        return false;
    }

    // Get collision profile instance
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (!CollisionProfile)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get collision profile instance"));
        return false;
    }

    // Determine collision response based on filter type
    ECollisionResponse FilterResponse = ECR_Ignore;
    if (FilterType == TEXT("include"))
    {
        FilterResponse = ECR_Block;
    }
    else if (FilterType == TEXT("exclude"))
    {
        FilterResponse = ECR_Ignore;
    }
    else if (FilterType == TEXT("priority"))
    {
        FilterResponse = ECR_Overlap;
    }

    // Apply filtering to existing collision profiles
    int32 ModifiedProfiles = 0;
    for (int32 ProfileIndex = 0; ProfileIndex < CollisionProfile->GetNumOfProfiles(); ProfileIndex++)
    {
        const FCollisionResponseTemplate* ProfilePtr = CollisionProfile->GetProfileByIndex(ProfileIndex);
        if (!ProfilePtr)
        {
            continue;
        }
        
        // Create a mutable copy to modify
        FCollisionResponseTemplate Profile = *ProfilePtr;
        
        // Check if this profile uses the source channel as object type
        if (Profile.ObjectType == SourceChannel)
        {
            // Apply filter to target channels
            for (ECollisionChannel TargetChannel : TargetChannels)
            {
                // Apply filter strength
                if (FilterStrength >= 1.0)
                {
                    Profile.ResponseToChannels.SetResponse(TargetChannel, FilterResponse);
                }
                else if (FilterStrength > 0.0)
                {
                    // Partial filtering - use overlap for gradual response
                    Profile.ResponseToChannels.SetResponse(TargetChannel, ECR_Overlap);
                }
            }
            
            ModifiedProfiles++;
            
            // Apply changes directly to actors using this profile
            UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
            if (World)
            {
                for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
                {
                    AActor* Actor = *ActorItr;
                    if (!Actor) continue;
                    
                    TArray<UPrimitiveComponent*> PrimitiveComponents;
                    Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);
                    
                    for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
                    {
                        if (!PrimComp || PrimComp->GetCollisionProfileName() != Profile.Name) continue;
                        
                        // Apply filter to target channels directly
                        for (ECollisionChannel TargetChannel : TargetChannels)
                        {
                            if (FilterStrength >= 1.0)
                            {
                                PrimComp->SetCollisionResponseToChannel(TargetChannel, FilterResponse);
                            }
                            else if (FilterStrength > 0.0)
                            {
                                PrimComp->SetCollisionResponseToChannel(TargetChannel, ECR_Overlap);
                            }
                        }
                    }
                }
            }
        }
    }

    // Apply filtering to existing actors if requested
    int32 FilteredActors = 0;
    if (bApplyToExistingActors)
    {
        UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
        if (World)
        {
            for (TActorIterator<AActor> ActorItr(World); ActorItr; ++ActorItr)
            {
                AActor* Actor = *ActorItr;
                if (!Actor)
                {
                    continue;
                }

                // Get all primitive components
                TArray<UPrimitiveComponent*> PrimitiveComponents;
                Actor->GetComponents<UPrimitiveComponent>(PrimitiveComponents);

                for (UPrimitiveComponent* PrimComp : PrimitiveComponents)
                {
                    if (!PrimComp || PrimComp->GetCollisionObjectType() != SourceChannel)
                    {
                        continue;
                    }

                    // Apply filtering to target channels
                    for (ECollisionChannel TargetChannel : TargetChannels)
                    {
                        PrimComp->SetCollisionResponseToChannel(TargetChannel, FilterResponse);
                    }

                    FilteredActors++;
                }
            }
        }
    }

    // Mark package as dirty to save changes
    CollisionProfile->MarkPackageDirty();

    UE_LOG(LogTemp, Display, TEXT("Successfully setup collision filtering for layer '%s'. Modified %d profiles and %d existing actors."), *LayerName, ModifiedProfiles, FilteredActors);
    return true;
}

bool FUnrealMCPCollisionCommands::ValidateCollisionChannelName(const FString& ChannelName)
{
    // Check if channel name is empty or too short
    if (ChannelName.IsEmpty() || ChannelName.Len() < 3)
    {
        return false;
    }

    // Check if channel name is too long (UE5 has practical limits)
    if (ChannelName.Len() > 64)
    {
        return false;
    }

    // Check for invalid characters (only alphanumeric and underscore allowed)
    for (int32 i = 0; i < ChannelName.Len(); i++)
    {
        TCHAR Char = ChannelName[i];
        if (!FChar::IsAlnum(Char) && Char != TEXT('_'))
        {
            return false;
        }
    }

    // Check if it starts with a letter or underscore (not a number)
    if (FChar::IsDigit(ChannelName[0]))
    {
        return false;
    }

    // Check against reserved collision channel names
    TArray<FString> ReservedNames = {
        TEXT("WorldStatic"),
        TEXT("WorldDynamic"),
        TEXT("Pawn"),
        TEXT("Visibility"),
        TEXT("Camera"),
        TEXT("PhysicsBody"),
        TEXT("Vehicle"),
        TEXT("Destructible")
    };

    for (const FString& ReservedName : ReservedNames)
    {
        if (ChannelName.Equals(ReservedName, ESearchCase::IgnoreCase))
        {
            return false;
        }
    }

    return true;
}

bool FUnrealMCPCollisionCommands::ValidateCollisionProfileName(const FString& ProfileName)
{
    // Check if profile name is empty or too short
    if (ProfileName.IsEmpty() || ProfileName.Len() < 3)
    {
        return false;
    }

    // Check if profile name is too long
    if (ProfileName.Len() > 64)
    {
        return false;
    }

    // Check for invalid characters (alphanumeric, underscore, and space allowed for profiles)
    for (int32 i = 0; i < ProfileName.Len(); i++)
    {
        TCHAR Char = ProfileName[i];
        if (!FChar::IsAlnum(Char) && Char != TEXT('_') && Char != TEXT(' '))
        {
            return false;
        }
    }

    // Check if it starts with a letter or underscore (not a number or space)
    if (FChar::IsDigit(ProfileName[0]) || ProfileName[0] == TEXT(' '))
    {
        return false;
    }

    // Check against reserved collision profile names
    TArray<FString> ReservedProfiles = {
        TEXT("NoCollision"),
        TEXT("BlockAll"),
        TEXT("OverlapAll"),
        TEXT("BlockAllDynamic"),
        TEXT("OverlapAllDynamic"),
        TEXT("IgnoreOnlyPawn"),
        TEXT("OverlapOnlyPawn"),
        TEXT("Pawn"),
        TEXT("Spectator"),
        TEXT("CharacterMesh"),
        TEXT("PhysicsActor"),
        TEXT("Destructible"),
        TEXT("InvisibleWall"),
        TEXT("InvisibleWallDynamic"),
        TEXT("Trigger"),
        TEXT("Ragdoll"),
        TEXT("Vehicle"),
        TEXT("UI")
    };

    for (const FString& ReservedProfile : ReservedProfiles)
    {
        if (ProfileName.Equals(ReservedProfile, ESearchCase::IgnoreCase))
        {
            return false;
        }
    }

    // Check if profile already exists
    UCollisionProfile* CollisionProfile = UCollisionProfile::Get();
    if (CollisionProfile)
    {
        FCollisionResponseTemplate ExistingProfile;
        if (CollisionProfile->GetProfileTemplate(*ProfileName, ExistingProfile))
        {
            // Profile already exists
            return false;
        }
    }

    return true;
}

bool FUnrealMCPCollisionCommands::ValidateLayerName(const FString& LayerName)
{
    // Layer names are essentially collision channel names in this context
    // so we can reuse the channel validation logic with some modifications
    
    // Check if layer name is empty or too short
    if (LayerName.IsEmpty() || LayerName.Len() < 2)
    {
        return false;
    }

    // Check if layer name is too long
    if (LayerName.Len() > 64)
    {
        return false;
    }

    // Check for invalid characters (alphanumeric, underscore, and hyphen allowed for layers)
    for (int32 i = 0; i < LayerName.Len(); i++)
    {
        TCHAR Char = LayerName[i];
        if (!FChar::IsAlnum(Char) && Char != TEXT('_') && Char != TEXT('-'))
        {
            return false;
        }
    }

    // Check if it starts with a letter or underscore (not a number or hyphen)
    if (FChar::IsDigit(LayerName[0]) || LayerName[0] == TEXT('-'))
    {
        return false;
    }

    // Check against reserved layer/channel names
    TArray<FString> ReservedLayers = {
        TEXT("Default"),
        TEXT("WorldStatic"),
        TEXT("WorldDynamic"),
        TEXT("Pawn"),
        TEXT("Visibility"),
        TEXT("Camera"),
        TEXT("PhysicsBody"),
        TEXT("Vehicle"),
        TEXT("Destructible"),
        TEXT("EngineTraceChannel1"),
        TEXT("EngineTraceChannel2"),
        TEXT("EngineTraceChannel3"),
        TEXT("EngineTraceChannel4"),
        TEXT("EngineTraceChannel5"),
        TEXT("EngineTraceChannel6")
    };

    for (const FString& ReservedLayer : ReservedLayers)
    {
        if (LayerName.Equals(ReservedLayer, ESearchCase::IgnoreCase))
        {
            return false;
        }
    }

    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::CreateErrorResponse(const FString& ErrorMessage)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), false);
    Response->SetStringField(TEXT("error"), ErrorMessage);
    
    UE_LOG(LogTemp, Error, TEXT("UnrealMCPCollisionCommands Error: %s"), *ErrorMessage);
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPCollisionCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), true);
    
    if (Data.IsValid())
    {
        // Copy all fields from Data to Response
        for (auto& Pair : Data->Values)
        {
            Response->SetField(Pair.Key, Pair.Value);
        }
    }
    
    return Response;
}