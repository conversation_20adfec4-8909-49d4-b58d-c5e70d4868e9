"C:/Game/Auracron/Plugins/UnrealMCP/Source/UnrealMCP/Private/Commands/UnrealMCPCollisionCommands.cpp"
@"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/UnrealMCP.Shared.rsp"
/external:W0
/external:I "C:/Program Files/Microsoft Visual Studio/2022/Community/VC/Tools/MSVC/14.44.35207/INCLUDE"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/ucrt"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/shared"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/um"
/external:I "C:/Program Files (x86)/Windows Kits/10/include/10.0.26100.0/winrt"
/FI"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/Definitions.UnrealMCP.h"
/Fo"C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/SingleFile/UnrealMCPCollisionCommands.cpp.obj"
/sourceDependencies "C:/Game/Auracron/Plugins/UnrealMCP/Intermediate/Build/Win64/x64/UnrealEditor/Development/UnrealMCP/SingleFile/UnrealMCPCollisionCommands.cpp.dep.json"
/Zc:inline
/nologo
/Oi
/FC
/diagnostics:caret
/c
/Gw
/Gy
/utf-8
/wd4819
/DSAL_NO_ATTRIBUTE_DECLARATIONS=1
/permissive-
/Zc:strictStrings-
/Zc:__cplusplus
/D_CRT_STDIO_LEGACY_WIDE_SPECIFIERS=1
/D_SILENCE_STDEXT_HASH_DEPRECATION_WARNINGS=1
/D_WINDLL
/D_DISABLE_EXTENDED_ALIGNED_STORAGE
/Ob2
/d2ExtendedWarningInfo
/Ox
/Ot
/GF
/errorReport:prompt
/EHsc
/DPLATFORM_EXCEPTIONS_DISABLED=0
/Z7
/MD
/bigobj
/fp:fast
/Zo
/Zp8
/W4
/we4456
/we4458
/we4459
/wd4244
/wd4838
/TP
/GR-
/std:c++20
/Zc:preprocessor
/wd5054
