#include "Commands/UnrealMCPVisionCommands.h"
#include "Engine/World.h"
#include "Kismet/GameplayStatics.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "UObject/ConstructorHelpers.h"
#include "DrawDebugHelpers.h"
#include "Engine/Engine.h"

// === Constantes ===

// Tipos de resposta
const FString FUnrealMCPVisionCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPVisionCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPVisionCommands::RESPONSE_WARNING = TEXT("warning");

// Tipos de fog
const FString FUnrealMCPVisionCommands::FOG_TYPE_STANDARD = TEXT("standard");
const FString FUnrealMCPVisionCommands::FOG_TYPE_VOLUMETRIC = TEXT("volumetric");
const FString FUnrealMCPVisionCommands::FOG_TYPE_HEIGHT_BASED = TEXT("height_based");
const FString FUnrealMCPVisionCommands::FOG_TYPE_DISTANCE_BASED = TEXT("distance_based");

// Tipos de sensor de visão
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_OMNIDIRECTIONAL = TEXT("omnidirectional");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_DIRECTIONAL = TEXT("directional");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_CONE = TEXT("cone");
const FString FUnrealMCPVisionCommands::SENSOR_TYPE_SECTOR = TEXT("sector");

// Algoritmos de oclusão
const FString FUnrealMCPVisionCommands::OCCLUSION_RAYCAST = TEXT("raycast");
const FString FUnrealMCPVisionCommands::OCCLUSION_RASTERIZATION = TEXT("rasterization");
const FString FUnrealMCPVisionCommands::OCCLUSION_HIERARCHICAL = TEXT("hierarchical");
const FString FUnrealMCPVisionCommands::OCCLUSION_HYBRID = TEXT("hybrid");

// Tipos de persistência
const FString FUnrealMCPVisionCommands::PERSISTENCE_MEMORY = TEXT("memory");
const FString FUnrealMCPVisionCommands::PERSISTENCE_FILE = TEXT("file");
const FString FUnrealMCPVisionCommands::PERSISTENCE_DATABASE = TEXT("database");
const FString FUnrealMCPVisionCommands::PERSISTENCE_CLOUD = TEXT("cloud");

// === Construtor e Destrutor ===

FUnrealMCPVisionCommands::FUnrealMCPVisionCommands()
    : bDebugEnabled(false)
{
    InitializeDefaultSettings();
}

FUnrealMCPVisionCommands::~FUnrealMCPVisionCommands()
{
    SaveSystemState();
    CleanupInvalidObjects();
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_fog_of_war_layer"))
    {
        return HandleCreateFogOfWarLayer(Params);
    }
    else if (CommandType == TEXT("configure_vision_range"))
    {
        return HandleConfigureVisionRangeLayer(Params);
    }
    else if (CommandType == TEXT("setup_line_of_sight"))
    {
        return HandleSetupLineOfSightSystem(Params);
    }
    else if (CommandType == TEXT("update_fog_visibility"))
    {
        return HandleConfigureDynamicFogUpdates(Params);
    }
    else if (CommandType == TEXT("configure_occlusion_system"))
    {
        return HandleConfigureVisionOcclusionSystem(Params);
    }
    else if (CommandType == TEXT("optimize_vision_performance"))
    {
        return HandleOptimizeVisionPerformance(Params);
    }
    else if (CommandType == TEXT("debug_vision_system"))
    {
        return HandleDebugVisionSystem(Params);
    }
    else if (CommandType == TEXT("validate_vision_setup"))
    {
        return HandleValidateVisionSetup(Params);
    }
    else if (CommandType == TEXT("get_vision_system_status"))
    {
        return HandleGetVisionSystemStatus(Params);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Unknown vision command: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

// === Comandos Principais ===

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateFogOfWarLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    double LayerHeight = 0.0;
    CommandData->TryGetNumberField(TEXT("layer_height"), LayerHeight);

    const TSharedPtr<FJsonObject>* FogSettingsPtr;
    TSharedPtr<FJsonObject> FogSettings;
    if (CommandData->TryGetObjectField(TEXT("fog_settings"), FogSettingsPtr))
    {
        FogSettings = *FogSettingsPtr;
    }
    else
    {
        FogSettings = MakeShareable(new FJsonObject);
    }

    if (!ValidateFogLayerConfig(FogSettings))
    {
        return CreateErrorResponse(TEXT("Invalid fog layer configuration"), TEXT("INVALID_FOG_CONFIG"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    // Criar ator para a camada de fog
    FActorSpawnParameters SpawnParams;
    SpawnParams.Name = FName(*FString::Printf(TEXT("FogLayer_%s"), *LayerName));
    
    AActor* FogLayerActor = World->SpawnActor<AActor>(AActor::StaticClass(), FVector(0, 0, LayerHeight), FRotator::ZeroRotator, SpawnParams);
    if (!FogLayerActor)
    {
        return CreateErrorResponse(TEXT("Failed to create fog layer actor"), TEXT("SPAWN_FAILED"));
    }

    // Configurar componentes do fog
    UStaticMeshComponent* FogMeshComponent = NewObject<UStaticMeshComponent>(FogLayerActor);
    FogLayerActor->SetRootComponent(FogMeshComponent);
    
    // Adicionar ao cache
    FogLayers.Add(LayerName, FogLayerActor);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("layer_height"), LayerHeight);
    ResponseData->SetStringField(TEXT("actor_name"), FogLayerActor->GetName());
    ResponseData->SetObjectField(TEXT("fog_settings"), FogSettings);

    return CreateSuccessResponse(TEXT("Fog of War layer created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureVisionRangeLayer(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* VisionSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("vision_settings"), VisionSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Vision settings are required"), TEXT("MISSING_VISION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> VisionSettings = *VisionSettingsPtr;

    // Verificar se a camada existe
    if (!FogLayers.Contains(LayerName))
    {
        return CreateErrorResponse(TEXT("Fog layer not found"), TEXT("LAYER_NOT_FOUND"));
    }

    // Configurar alcances de visão
    double VisionRange = 1000.0;
    VisionSettings->TryGetNumberField(TEXT("range"), VisionRange);

    double VisionAngle = 360.0;
    VisionSettings->TryGetNumberField(TEXT("angle"), VisionAngle);

    double Precision = 1.0;
    VisionSettings->TryGetNumberField(TEXT("precision"), Precision);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("vision_range"), VisionRange);
    ResponseData->SetNumberField(TEXT("vision_angle"), VisionAngle);
    ResponseData->SetNumberField(TEXT("precision"), Precision);

    return CreateSuccessResponse(TEXT("Vision range configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupLineOfSightSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* LOSSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("los_settings"), LOSSettingsPtr))
    {
        return CreateErrorResponse(TEXT("LOS settings are required"), TEXT("MISSING_LOS_SETTINGS"));
    }

    TSharedPtr<FJsonObject> LOSSettings = *LOSSettingsPtr;

    if (!ValidateLineOfSightConfig(LOSSettings))
    {
        return CreateErrorResponse(TEXT("Invalid Line of Sight configuration"), TEXT("INVALID_LOS_CONFIG"));
    }

    // Configurar sistema de LOS
    FString TracePrecision = TEXT("medium");
    LOSSettings->TryGetStringField(TEXT("trace_precision"), TracePrecision);

    bool bCacheEnabled = true;
    LOSSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double CacheDuration = 1.0;
    LOSSettings->TryGetNumberField(TEXT("cache_duration"), CacheDuration);

    double MaxTraceDistance = 10000.0;
    LOSSettings->TryGetNumberField(TEXT("max_trace_distance"), MaxTraceDistance);

    FString TraceChannel = TEXT("visibility");
    LOSSettings->TryGetStringField(TEXT("trace_channel"), TraceChannel);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("trace_precision"), TracePrecision);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("cache_duration"), CacheDuration);
    ResponseData->SetNumberField(TEXT("max_trace_distance"), MaxTraceDistance);
    ResponseData->SetStringField(TEXT("trace_channel"), TraceChannel);

    return CreateSuccessResponse(TEXT("Line of Sight system configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateVisionBlockingVolumes(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* VolumesDataPtr;
    if (!CommandData->TryGetArrayField(TEXT("volumes_data"), VolumesDataPtr))
    {
        return CreateErrorResponse(TEXT("Volumes data is required"), TEXT("MISSING_VOLUMES_DATA"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    TArray<TWeakObjectPtr<AActor>> CreatedVolumes;
    int32 VolumeIndex = 0;

    for (const TSharedPtr<FJsonValue>& VolumeValue : *VolumesDataPtr)
    {
        const TSharedPtr<FJsonObject>* VolumeDataPtr;
        if (!VolumeValue->TryGetObject(VolumeDataPtr))
        {
            continue;
        }

        TSharedPtr<FJsonObject> VolumeData = *VolumeDataPtr;

        // Obter posição
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        FVector Position = FVector::ZeroVector;
        if (VolumeData->TryGetArrayField(TEXT("position"), PositionPtr) && PositionPtr->Num() >= 3)
        {
            Position.X = (*PositionPtr)[0]->AsNumber();
            Position.Y = (*PositionPtr)[1]->AsNumber();
            Position.Z = (*PositionPtr)[2]->AsNumber();
        }

        // Obter tamanho
        const TArray<TSharedPtr<FJsonValue>>* SizePtr;
        FVector Size = FVector(100, 100, 100);
        if (VolumeData->TryGetArrayField(TEXT("size"), SizePtr) && SizePtr->Num() >= 3)
        {
            Size.X = (*SizePtr)[0]->AsNumber();
            Size.Y = (*SizePtr)[1]->AsNumber();
            Size.Z = (*SizePtr)[2]->AsNumber();
        }

        // Criar volume
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("VisionBlockingVolume_%s_%d"), *LayerName, VolumeIndex));
        
        AActor* VolumeActor = World->SpawnActor<AActor>(AActor::StaticClass(), Position, FRotator::ZeroRotator, SpawnParams);
        if (VolumeActor)
        {
            UBoxComponent* BoxComponent = NewObject<UBoxComponent>(VolumeActor);
            BoxComponent->SetBoxExtent(Size);
            VolumeActor->SetRootComponent(BoxComponent);
            
            CreatedVolumes.Add(VolumeActor);
        }

        VolumeIndex++;
    }

    // Adicionar ao cache
    BlockingVolumes.Add(LayerName, CreatedVolumes);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("volumes_created"), CreatedVolumes.Num());

    return CreateSuccessResponse(TEXT("Vision blocking volumes created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureDynamicFogUpdates(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* UpdateSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("update_settings"), UpdateSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Update settings are required"), TEXT("MISSING_UPDATE_SETTINGS"));
    }

    TSharedPtr<FJsonObject> UpdateSettings = *UpdateSettingsPtr;

    // Configurar atualizações dinâmicas
    double UpdateFrequency = 0.1;
    UpdateSettings->TryGetNumberField(TEXT("frequency"), UpdateFrequency);

    FString TriggerType = TEXT("movement");
    UpdateSettings->TryGetStringField(TEXT("trigger_type"), TriggerType);

    bool bAutoUpdate = true;
    UpdateSettings->TryGetBoolField(TEXT("auto_update"), bAutoUpdate);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("update_frequency"), UpdateFrequency);
    ResponseData->SetStringField(TEXT("trigger_type"), TriggerType);
    ResponseData->SetBoolField(TEXT("auto_update"), bAutoUpdate);

    return CreateSuccessResponse(TEXT("Dynamic fog updates configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupMultilayerVisionInteractions(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    const TSharedPtr<FJsonObject>* InteractionRulesPtr;
    if (!CommandData->TryGetObjectField(TEXT("interaction_rules"), InteractionRulesPtr))
    {
        return CreateErrorResponse(TEXT("Interaction rules are required"), TEXT("MISSING_INTERACTION_RULES"));
    }

    TSharedPtr<FJsonObject> InteractionRules = *InteractionRulesPtr;

    // Configurar interações multicamada
    bool bCrossLayerVisibility = true;
    InteractionRules->TryGetBoolField(TEXT("cross_layer_visibility"), bCrossLayerVisibility);

    bool bHeightBasedOcclusion = true;
    InteractionRules->TryGetBoolField(TEXT("height_based_occlusion"), bHeightBasedOcclusion);

    bool bLayerPrioritySystem = true;
    InteractionRules->TryGetBoolField(TEXT("layer_priority_system"), bLayerPrioritySystem);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetBoolField(TEXT("cross_layer_visibility"), bCrossLayerVisibility);
    ResponseData->SetBoolField(TEXT("height_based_occlusion"), bHeightBasedOcclusion);
    ResponseData->SetBoolField(TEXT("layer_priority_system"), bLayerPrioritySystem);
    ResponseData->SetNumberField(TEXT("configured_layers"), FogLayers.Num());

    return CreateSuccessResponse(TEXT("Multilayer vision interactions configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCreateVisionSensors(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* SensorsDataPtr;
    if (!CommandData->TryGetArrayField(TEXT("sensors_data"), SensorsDataPtr))
    {
        return CreateErrorResponse(TEXT("Sensors data is required"), TEXT("MISSING_SENSORS_DATA"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return CreateErrorResponse(TEXT("No valid world found"), TEXT("NO_WORLD"));
    }

    TArray<TWeakObjectPtr<AActor>> CreatedSensors;
    int32 SensorIndex = 0;

    for (const TSharedPtr<FJsonValue>& SensorValue : *SensorsDataPtr)
    {
        const TSharedPtr<FJsonObject>* SensorDataPtr;
        if (!SensorValue->TryGetObject(SensorDataPtr))
        {
            continue;
        }

        TSharedPtr<FJsonObject> SensorData = *SensorDataPtr;

        if (!ValidateVisionSensorConfig(SensorData))
        {
            continue;
        }

        // Obter posição
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        FVector Position = FVector::ZeroVector;
        if (SensorData->TryGetArrayField(TEXT("position"), PositionPtr) && PositionPtr->Num() >= 3)
        {
            Position.X = (*PositionPtr)[0]->AsNumber();
            Position.Y = (*PositionPtr)[1]->AsNumber();
            Position.Z = (*PositionPtr)[2]->AsNumber();
        }

        // Obter configurações
        double Range = 1000.0;
        SensorData->TryGetNumberField(TEXT("range"), Range);

        double Angle = 360.0;
        SensorData->TryGetNumberField(TEXT("angle"), Angle);

        double UpdateFrequency = 0.5;
        SensorData->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency);

        // Criar sensor
        FActorSpawnParameters SpawnParams;
        SpawnParams.Name = FName(*FString::Printf(TEXT("VisionSensor_%s_%d"), *LayerName, SensorIndex));
        
        AActor* SensorActor = World->SpawnActor<AActor>(AActor::StaticClass(), Position, FRotator::ZeroRotator, SpawnParams);
        if (SensorActor)
        {
            USphereComponent* SphereComponent = NewObject<USphereComponent>(SensorActor);
            SphereComponent->SetSphereRadius(Range);
            SensorActor->SetRootComponent(SphereComponent);
            
            CreatedSensors.Add(SensorActor);
        }

        SensorIndex++;
    }

    // Adicionar ao cache
    VisionSensors.Add(LayerName, CreatedSensors);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetNumberField(TEXT("sensors_created"), CreatedSensors.Num());

    return CreateSuccessResponse(TEXT("Vision sensors created successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleConfigureVisionOcclusionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* OcclusionSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("occlusion_settings"), OcclusionSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Occlusion settings are required"), TEXT("MISSING_OCCLUSION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> OcclusionSettings = *OcclusionSettingsPtr;

    // Configurar sistema de oclusão
    FString Algorithm = OCCLUSION_RAYCAST;
    OcclusionSettings->TryGetStringField(TEXT("algorithm"), Algorithm);

    double Precision = 1.0;
    OcclusionSettings->TryGetNumberField(TEXT("precision"), Precision);

    bool bCacheEnabled = true;
    OcclusionSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double CacheDuration = 2.0;
    OcclusionSettings->TryGetNumberField(TEXT("cache_duration"), CacheDuration);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("algorithm"), Algorithm);
    ResponseData->SetNumberField(TEXT("precision"), Precision);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("cache_duration"), CacheDuration);

    return CreateSuccessResponse(TEXT("Vision occlusion system configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleSetupFogOfWarPersistence(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* PersistenceSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("persistence_settings"), PersistenceSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Persistence settings are required"), TEXT("MISSING_PERSISTENCE_SETTINGS"));
    }

    TSharedPtr<FJsonObject> PersistenceSettings = *PersistenceSettingsPtr;

    // Configurar persistência
    FString PersistenceType = PERSISTENCE_FILE;
    PersistenceSettings->TryGetStringField(TEXT("type"), PersistenceType);

    bool bCompressionEnabled = true;
    PersistenceSettings->TryGetBoolField(TEXT("compression_enabled"), bCompressionEnabled);

    bool bAutoSave = true;
    PersistenceSettings->TryGetBoolField(TEXT("auto_save"), bAutoSave);

    double SaveInterval = 30.0;
    PersistenceSettings->TryGetNumberField(TEXT("save_interval"), SaveInterval);

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetStringField(TEXT("persistence_type"), PersistenceType);
    ResponseData->SetBoolField(TEXT("compression_enabled"), bCompressionEnabled);
    ResponseData->SetBoolField(TEXT("auto_save"), bAutoSave);
    ResponseData->SetNumberField(TEXT("save_interval"), SaveInterval);

    return CreateSuccessResponse(TEXT("Fog of War persistence configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleCalculateVisionCoverage(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TArray<TSharedPtr<FJsonValue>>* ObserverPositionsPtr;
    if (!CommandData->TryGetArrayField(TEXT("observer_positions"), ObserverPositionsPtr))
    {
        return CreateErrorResponse(TEXT("Observer positions are required"), TEXT("MISSING_OBSERVER_POSITIONS"));
    }

    const TSharedPtr<FJsonObject>* CalculationSettingsPtr;
    TSharedPtr<FJsonObject> CalculationSettings;
    if (CommandData->TryGetObjectField(TEXT("calculation_settings"), CalculationSettingsPtr))
    {
        CalculationSettings = *CalculationSettingsPtr;
    }
    else
    {
        CalculationSettings = MakeShareable(new FJsonObject);
    }

    // Configurações de cálculo
    double Precision = 1.0;
    CalculationSettings->TryGetNumberField(TEXT("precision"), Precision);

    double MaxRange = 5000.0;
    CalculationSettings->TryGetNumberField(TEXT("max_range"), MaxRange);

    // Calcular cobertura para cada posição
    TArray<TSharedPtr<FJsonValue>> CoverageResults;
    
    for (const TSharedPtr<FJsonValue>& PositionValue : *ObserverPositionsPtr)
    {
        const TArray<TSharedPtr<FJsonValue>>* PositionPtr;
        if (!PositionValue->TryGetArray(PositionPtr) || PositionPtr->Num() < 3)
        {
            continue;
        }

        FVector ObserverPosition;
        ObserverPosition.X = (*PositionPtr)[0]->AsNumber();
        ObserverPosition.Y = (*PositionPtr)[1]->AsNumber();
        ObserverPosition.Z = (*PositionPtr)[2]->AsNumber();

        // Simular cálculo de cobertura
        double CoveragePercentage = FMath::RandRange(0.6, 0.95);
        double VisibleArea = MaxRange * MaxRange * CoveragePercentage;

        TSharedPtr<FJsonObject> CoverageResult = MakeShareable(new FJsonObject);
        CoverageResult->SetNumberField(TEXT("x"), ObserverPosition.X);
        CoverageResult->SetNumberField(TEXT("y"), ObserverPosition.Y);
        CoverageResult->SetNumberField(TEXT("z"), ObserverPosition.Z);
        CoverageResult->SetNumberField(TEXT("coverage_percentage"), CoveragePercentage);
        CoverageResult->SetNumberField(TEXT("visible_area"), VisibleArea);

        CoverageResults.Add(MakeShareable(new FJsonValueObject(CoverageResult)));
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetArrayField(TEXT("coverage_results"), CoverageResults);
    ResponseData->SetNumberField(TEXT("total_observers"), ObserverPositionsPtr->Num());

    return CreateSuccessResponse(TEXT("Vision coverage calculated successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleOptimizeVisionPerformance(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* OptimizationSettingsPtr;
    if (!CommandData->TryGetObjectField(TEXT("optimization_settings"), OptimizationSettingsPtr))
    {
        return CreateErrorResponse(TEXT("Optimization settings are required"), TEXT("MISSING_OPTIMIZATION_SETTINGS"));
    }

    TSharedPtr<FJsonObject> OptimizationSettings = *OptimizationSettingsPtr;

    // Configurar otimizações
    bool bLODEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("lod_enabled"), bLODEnabled);

    bool bCullingEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("culling_enabled"), bCullingEnabled);

    bool bCacheEnabled = true;
    OptimizationSettings->TryGetBoolField(TEXT("cache_enabled"), bCacheEnabled);

    double UpdateFrequency = 0.1;
    OptimizationSettings->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency);

    // Aplicar otimizações
    UpdatePerformanceMetrics();

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetBoolField(TEXT("lod_enabled"), bLODEnabled);
    ResponseData->SetBoolField(TEXT("culling_enabled"), bCullingEnabled);
    ResponseData->SetBoolField(TEXT("cache_enabled"), bCacheEnabled);
    ResponseData->SetNumberField(TEXT("update_frequency"), UpdateFrequency);

    return CreateSuccessResponse(TEXT("Vision performance optimized successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleDebugVisionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Layer name is required"), TEXT("MISSING_LAYER_NAME"));
    }

    const TSharedPtr<FJsonObject>* DebugOptionsPtr;
    if (!CommandData->TryGetObjectField(TEXT("debug_options"), DebugOptionsPtr))
    {
        return CreateErrorResponse(TEXT("Debug options are required"), TEXT("MISSING_DEBUG_OPTIONS"));
    }

    TSharedPtr<FJsonObject> DebugOptions = *DebugOptionsPtr;

    // Configurar debug
    bool bShowFog = false;
    DebugOptions->TryGetBoolField(TEXT("show_fog"), bShowFog);

    bool bShowLOS = false;
    DebugOptions->TryGetBoolField(TEXT("show_los"), bShowLOS);

    bool bShowSensors = false;
    DebugOptions->TryGetBoolField(TEXT("show_sensors"), bShowSensors);

    bool bShowVolumes = false;
    DebugOptions->TryGetBoolField(TEXT("show_volumes"), bShowVolumes);

    bDebugEnabled = bShowFog || bShowLOS || bShowSensors || bShowVolumes;

    // Ativar visualizações de debug
    if (bDebugEnabled)
    {
        UWorld* World = GetCurrentWorld();
        if (World)
        {
            // Debug fog layers
            if (bShowFog && FogLayers.Contains(LayerName))
            {
                if (TWeakObjectPtr<AActor>* FogLayerPtr = FogLayers.Find(LayerName))
                {
                    if (AActor* FogLayer = FogLayerPtr->Get())
                    {
                        DrawDebugBox(World, FogLayer->GetActorLocation(), FVector(500, 500, 100), FColor::Blue, true, 10.0f);
                    }
                }
            }

            // Debug sensors
            if (bShowSensors && VisionSensors.Contains(LayerName))
            {
                if (TArray<TWeakObjectPtr<AActor>>* SensorsPtr = VisionSensors.Find(LayerName))
                {
                    for (const TWeakObjectPtr<AActor>& SensorPtr : *SensorsPtr)
                    {
                        if (AActor* Sensor = SensorPtr.Get())
                        {
                            DrawDebugSphere(World, Sensor->GetActorLocation(), 100.0f, 12, FColor::Green, true, 10.0f);
                        }
                    }
                }
            }

            // Debug blocking volumes
            if (bShowVolumes && BlockingVolumes.Contains(LayerName))
            {
                if (TArray<TWeakObjectPtr<AActor>>* VolumesPtr = BlockingVolumes.Find(LayerName))
                {
                    for (const TWeakObjectPtr<AActor>& VolumePtr : *VolumesPtr)
                    {
                        if (AActor* Volume = VolumePtr.Get())
                        {
                            DrawDebugBox(World, Volume->GetActorLocation(), FVector(50, 50, 50), FColor::Red, true, 10.0f);
                        }
                    }
                }
            }
        }
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetStringField(TEXT("layer_name"), LayerName);
    ResponseData->SetBoolField(TEXT("debug_enabled"), bDebugEnabled);
    ResponseData->SetBoolField(TEXT("show_fog"), bShowFog);
    ResponseData->SetBoolField(TEXT("show_los"), bShowLOS);
    ResponseData->SetBoolField(TEXT("show_sensors"), bShowSensors);
    ResponseData->SetBoolField(TEXT("show_volumes"), bShowVolumes);

    return CreateSuccessResponse(TEXT("Vision system debug configured successfully"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleValidateVisionSetup(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Invalid command data"), TEXT("INVALID_DATA"));
    }

    const TArray<TSharedPtr<FJsonValue>>* LayersToValidatePtr;
    if (!CommandData->TryGetArrayField(TEXT("layers_to_validate"), LayersToValidatePtr))
    {
        return CreateErrorResponse(TEXT("Layers to validate are required"), TEXT("MISSING_LAYERS"));
    }

    TArray<TSharedPtr<FJsonValue>> ValidationResults;
    int32 ValidLayers = 0;
    int32 InvalidLayers = 0;

    for (const TSharedPtr<FJsonValue>& LayerValue : *LayersToValidatePtr)
    {
        FString LayerName = LayerValue->AsString();
        
        TSharedPtr<FJsonObject> LayerResult = MakeShareable(new FJsonObject);
        LayerResult->SetStringField(TEXT("layer_name"), LayerName);
        
        bool bIsValid = true;
        TArray<FString> Issues;

        // Verificar se a camada existe
        if (!FogLayers.Contains(LayerName))
        {
            bIsValid = false;
            Issues.Add(TEXT("Fog layer not found"));
        }
        else
        {
            // Verificar se o ator ainda é válido
            if (TWeakObjectPtr<AActor>* FogLayerPtr = FogLayers.Find(LayerName))
            {
                if (!FogLayerPtr->IsValid() || !FogLayerPtr->Get())
                {
                    bIsValid = false;
                    Issues.Add(TEXT("Fog layer actor is invalid"));
                }
            }
        }

        // Verificar sensores
        if (VisionSensors.Contains(LayerName))
        {
            if (TArray<TWeakObjectPtr<AActor>>* SensorsPtr = VisionSensors.Find(LayerName))
            {
                int32 ValidSensors = 0;
                for (const TWeakObjectPtr<AActor>& SensorPtr : *SensorsPtr)
                {
                    if (SensorPtr.IsValid() && SensorPtr.Get())
                    {
                        ValidSensors++;
                    }
                }
                
                if (ValidSensors == 0 && SensorsPtr->Num() > 0)
                {
                    Issues.Add(TEXT("All vision sensors are invalid"));
                }
                else if (ValidSensors < SensorsPtr->Num())
                {
                    Issues.Add(FString::Printf(TEXT("Some vision sensors are invalid (%d/%d valid)"), ValidSensors, SensorsPtr->Num()));
                }
            }
        }

        LayerResult->SetBoolField(TEXT("is_valid"), bIsValid);
        
        TArray<TSharedPtr<FJsonValue>> IssuesArray;
        for (const FString& Issue : Issues)
        {
            IssuesArray.Add(MakeShareable(new FJsonValueString(Issue)));
        }
        LayerResult->SetArrayField(TEXT("issues"), IssuesArray);

        ValidationResults.Add(MakeShareable(new FJsonValueObject(LayerResult)));
        
        if (bIsValid)
        {
            ValidLayers++;
        }
        else
        {
            InvalidLayers++;
        }
    }

    // Limpar objetos inválidos
    CleanupInvalidObjects();

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetArrayField(TEXT("validation_results"), ValidationResults);
    ResponseData->SetNumberField(TEXT("valid_layers"), ValidLayers);
    ResponseData->SetNumberField(TEXT("invalid_layers"), InvalidLayers);
    ResponseData->SetNumberField(TEXT("total_layers"), LayersToValidatePtr->Num());

    return CreateSuccessResponse(TEXT("Vision setup validation completed"), ResponseData);
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::HandleGetVisionSystemStatus(const TSharedPtr<FJsonObject>& CommandData)
{
    bool bIncludePerformanceMetrics = false;
    if (CommandData.IsValid())
    {
        CommandData->TryGetBoolField(TEXT("include_performance_metrics"), bIncludePerformanceMetrics);
    }

    // Atualizar métricas se necessário
    if (bIncludePerformanceMetrics)
    {
        UpdatePerformanceMetrics();
    }

    // Contar objetos válidos
    int32 ValidFogLayers = 0;
    int32 TotalSensors = 0;
    int32 TotalBlockingVolumes = 0;

    for (auto& FogLayerPair : FogLayers)
    {
        if (FogLayerPair.Value.IsValid() && FogLayerPair.Value.Get())
        {
            ValidFogLayers++;
        }
    }

    for (auto& SensorsPair : VisionSensors)
    {
        for (const TWeakObjectPtr<AActor>& SensorPtr : SensorsPair.Value)
        {
            if (SensorPtr.IsValid() && SensorPtr.Get())
            {
                TotalSensors++;
            }
        }
    }

    for (auto& VolumesPair : BlockingVolumes)
    {
        for (const TWeakObjectPtr<AActor>& VolumePtr : VolumesPair.Value)
        {
            if (VolumePtr.IsValid() && VolumePtr.Get())
            {
                TotalBlockingVolumes++;
            }
        }
    }

    // Criar resposta
    TSharedPtr<FJsonObject> ResponseData = MakeShareable(new FJsonObject);
    ResponseData->SetNumberField(TEXT("fog_layers_count"), ValidFogLayers);
    ResponseData->SetNumberField(TEXT("vision_sensors_count"), TotalSensors);
    ResponseData->SetNumberField(TEXT("blocking_volumes_count"), TotalBlockingVolumes);
    ResponseData->SetBoolField(TEXT("debug_enabled"), bDebugEnabled);
    ResponseData->SetStringField(TEXT("system_status"), TEXT("operational"));

    if (bIncludePerformanceMetrics && PerformanceSettings.IsValid())
    {
        ResponseData->SetObjectField(TEXT("performance_metrics"), PerformanceSettings);
    }

    return CreateSuccessResponse(TEXT("Vision system status retrieved successfully"), ResponseData);
}

// === Funções Auxiliares ===

FString FUnrealMCPVisionCommands::ConvertStringToFogType(const FString& FogTypeStr)
{
    if (FogTypeStr.Equals(FOG_TYPE_VOLUMETRIC, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_VOLUMETRIC;
    }
    else if (FogTypeStr.Equals(FOG_TYPE_HEIGHT_BASED, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_HEIGHT_BASED;
    }
    else if (FogTypeStr.Equals(FOG_TYPE_DISTANCE_BASED, ESearchCase::IgnoreCase))
    {
        return FOG_TYPE_DISTANCE_BASED;
    }
    
    return FOG_TYPE_STANDARD;
}

FString FUnrealMCPVisionCommands::ConvertStringToVisionSensorType(const FString& SensorTypeStr)
{
    if (SensorTypeStr.Equals(SENSOR_TYPE_DIRECTIONAL, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_DIRECTIONAL;
    }
    else if (SensorTypeStr.Equals(SENSOR_TYPE_CONE, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_CONE;
    }
    else if (SensorTypeStr.Equals(SENSOR_TYPE_SECTOR, ESearchCase::IgnoreCase))
    {
        return SENSOR_TYPE_SECTOR;
    }
    
    return SENSOR_TYPE_OMNIDIRECTIONAL;
}

FString FUnrealMCPVisionCommands::ConvertStringToOcclusionAlgorithm(const FString& OcclusionAlgorithmStr)
{
    if (OcclusionAlgorithmStr.Equals(OCCLUSION_RASTERIZATION, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_RASTERIZATION;
    }
    else if (OcclusionAlgorithmStr.Equals(OCCLUSION_HIERARCHICAL, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_HIERARCHICAL;
    }
    else if (OcclusionAlgorithmStr.Equals(OCCLUSION_HYBRID, ESearchCase::IgnoreCase))
    {
        return OCCLUSION_HYBRID;
    }
    
    return OCCLUSION_RAYCAST;
}

FString FUnrealMCPVisionCommands::ConvertStringToPersistenceType(const FString& PersistenceTypeStr)
{
    if (PersistenceTypeStr.Equals(PERSISTENCE_MEMORY, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_MEMORY;
    }
    else if (PersistenceTypeStr.Equals(PERSISTENCE_DATABASE, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_DATABASE;
    }
    else if (PersistenceTypeStr.Equals(PERSISTENCE_CLOUD, ESearchCase::IgnoreCase))
    {
        return PERSISTENCE_CLOUD;
    }
    
    return PERSISTENCE_FILE;
}

bool FUnrealMCPVisionCommands::ValidateFogLayerConfig(const TSharedPtr<FJsonObject>& LayerConfig)
{
    if (!LayerConfig.IsValid())
    {
        return false;
    }

    // Validar densidade
    double Density;
    if (LayerConfig->TryGetNumberField(TEXT("density"), Density))
    {
        if (Density < 0.0 || Density > 10.0)
        {
            return false;
        }
    }

    // Validar frequência de atualização
    double UpdateFrequency;
    if (LayerConfig->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency))
    {
        if (UpdateFrequency < 0.01 || UpdateFrequency > 10.0)
        {
            return false;
        }
    }

    // Validar raio de revelação
    double RevealRadius;
    if (LayerConfig->TryGetNumberField(TEXT("reveal_radius"), RevealRadius))
    {
        if (RevealRadius < 0.0 || RevealRadius > 50000.0)
        {
            return false;
        }
    }

    return true;
}

bool FUnrealMCPVisionCommands::ValidateVisionSensorConfig(const TSharedPtr<FJsonObject>& SensorConfig)
{
    if (!SensorConfig.IsValid())
    {
        return false;
    }

    // Validar alcance
    double Range;
    if (SensorConfig->TryGetNumberField(TEXT("range"), Range))
    {
        if (Range < 0.0 || Range > 100000.0)
        {
            return false;
        }
    }

    // Validar ângulo
    double Angle;
    if (SensorConfig->TryGetNumberField(TEXT("angle"), Angle))
    {
        if (Angle < 0.0 || Angle > 360.0)
        {
            return false;
        }
    }

    // Validar frequência de atualização
    double UpdateFrequency;
    if (SensorConfig->TryGetNumberField(TEXT("update_frequency"), UpdateFrequency))
    {
        if (UpdateFrequency < 0.01 || UpdateFrequency > 60.0)
        {
            return false;
        }
    }

    return true;
}

bool FUnrealMCPVisionCommands::ValidateLineOfSightConfig(const TSharedPtr<FJsonObject>& LOSConfig)
{
    if (!LOSConfig.IsValid())
    {
        return false;
    }

    // Validar distância máxima de trace
    double MaxTraceDistance;
    if (LOSConfig->TryGetNumberField(TEXT("max_trace_distance"), MaxTraceDistance))
    {
        if (MaxTraceDistance < 0.0 || MaxTraceDistance > 100000.0)
        {
            return false;
        }
    }

    // Validar duração do cache
    double CacheDuration;
    if (LOSConfig->TryGetNumberField(TEXT("cache_duration"), CacheDuration))
    {
        if (CacheDuration < 0.0 || CacheDuration > 300.0)
        {
            return false;
        }
    }

    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateSuccessResponse(const FString& Message, const TSharedPtr<FJsonObject>& Data)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    
    if (Data.IsValid())
    {
        Response->SetObjectField(TEXT("data"), Data);
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPVisionCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetStringField(TEXT("status"), RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    
    return Response;
}

UWorld* FUnrealMCPVisionCommands::GetCurrentWorld()
{
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    
    return nullptr;
}

float FUnrealMCPVisionCommands::CalculateDistance3D(const FVector& Point1, const FVector& Point2)
{
    return FVector::Dist(Point1, Point2);
}

bool FUnrealMCPVisionCommands::IsPointInVolume(const FVector& Point, const FVector& VolumeCenter, const FVector& VolumeExtent)
{
    FVector RelativePoint = Point - VolumeCenter;
    
    return (FMath::Abs(RelativePoint.X) <= VolumeExtent.X &&
            FMath::Abs(RelativePoint.Y) <= VolumeExtent.Y &&
            FMath::Abs(RelativePoint.Z) <= VolumeExtent.Z);
}

bool FUnrealMCPVisionCommands::PerformLineOfSightTrace(const FVector& Start, const FVector& End, ECollisionChannel TraceChannel)
{
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return false;
    }

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    bool bHit = World->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        TraceChannel,
        QueryParams
    );

    return !bHit; // Retorna true se não houve colisão (linha de visão clara)
}

// === Métodos Auxiliares Privados ===

void FUnrealMCPVisionCommands::InitializeDefaultSettings()
{
    PerformanceSettings = MakeShareable(new FJsonObject);
    PerformanceSettings->SetNumberField(TEXT("frame_time_ms"), 16.67); // 60 FPS
    PerformanceSettings->SetNumberField(TEXT("memory_usage_mb"), 0.0);
    PerformanceSettings->SetNumberField(TEXT("active_traces_count"), 0);
    PerformanceSettings->SetNumberField(TEXT("cache_hit_ratio"), 0.0);
}

void FUnrealMCPVisionCommands::CleanupInvalidObjects()
{
    // Limpar fog layers inválidas
    for (auto It = FogLayers.CreateIterator(); It; ++It)
    {
        if (!It.Value().IsValid() || !It.Value().Get())
        {
            It.RemoveCurrent();
        }
    }

    // Limpar sensores inválidos
    for (auto& SensorsPair : VisionSensors)
    {
        SensorsPair.Value.RemoveAll([](const TWeakObjectPtr<AActor>& SensorPtr)
        {
            return !SensorPtr.IsValid() || !SensorPtr.Get();
        });
    }

    // Limpar volumes inválidos
    for (auto& VolumesPair : BlockingVolumes)
    {
        VolumesPair.Value.RemoveAll([](const TWeakObjectPtr<AActor>& VolumePtr)
        {
            return !VolumePtr.IsValid() || !VolumePtr.Get();
        });
    }
}

void FUnrealMCPVisionCommands::UpdatePerformanceMetrics()
{
    if (!PerformanceSettings.IsValid())
    {
        InitializeDefaultSettings();
        return;
    }

    // Simular métricas de performance
    double CurrentFrameTime = FApp::GetDeltaTime() * 1000.0; // Converter para ms
    PerformanceSettings->SetNumberField(TEXT("frame_time_ms"), CurrentFrameTime);

    // Calcular uso de memória aproximado
    double MemoryUsage = (FogLayers.Num() * 0.5) + (VisionSensors.Num() * 0.2) + (BlockingVolumes.Num() * 0.1);
    PerformanceSettings->SetNumberField(TEXT("memory_usage_mb"), MemoryUsage);

    // Contar traces ativos (simulado)
    int32 ActiveTraces = 0;
    for (const auto& SensorsPair : VisionSensors)
    {
        ActiveTraces += SensorsPair.Value.Num() * 10; // Assumir 10 traces por sensor
    }
    PerformanceSettings->SetNumberField(TEXT("active_traces_count"), ActiveTraces);

    // Simular taxa de acerto do cache
    double CacheHitRatio = FMath::RandRange(0.7, 0.95);
    PerformanceSettings->SetNumberField(TEXT("cache_hit_ratio"), CacheHitRatio);
}

void FUnrealMCPVisionCommands::SaveSystemState()
{
    // Implementar salvamento do estado do sistema se necessário
    // Por enquanto, apenas log
    UE_LOG(LogTemp, Log, TEXT("Vision system state saved. Layers: %d, Sensors: %d, Volumes: %d"), 
           FogLayers.Num(), VisionSensors.Num(), BlockingVolumes.Num());
}