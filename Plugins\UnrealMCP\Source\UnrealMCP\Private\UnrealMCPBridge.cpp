#include "UnrealMCPBridge.h"
#include "MCPServerRunnable.h"
#include "Commands/UnrealMCPWorldPartitionCommands.h"
#include "Commands/UnrealMCPCollisionCommands.h"
#include "Commands/UnrealMCPPathfindingCommands.h"
#include "Commands/UnrealMCPVisionCommands.h"
#include "Commands/UnrealMCPAICommands.h"
#include "Commands/UnrealMCPRealmCommands.h"
#include "Commands/UnrealMCPNetworkCommands.h"
#include "Commands/UnrealMCPProceduralCommands.h"
#include "Commands/UnrealMCPPerformanceCommands.h"
#include "Sockets.h"
#include "SocketSubsystem.h"
#include "HAL/RunnableThread.h"
#include "Interfaces/IPv4/IPv4Address.h"
#include "Interfaces/IPv4/IPv4Endpoint.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonReader.h"
#include "Serialization/JsonWriter.h"
#include "Engine/StaticMeshActor.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Camera/CameraActor.h"
#include "EditorAssetLibrary.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "JsonObjectConverter.h"
#include "GameFramework/Actor.h"
#include "Engine/Selection.h"
#include "Kismet/GameplayStatics.h"
#include "Async/Async.h"
// Add Blueprint related includes
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Factories/BlueprintFactory.h"
#include "EdGraphSchema_K2.h"
#include "K2Node_Event.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "Components/StaticMeshComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "Kismet2/KismetEditorUtilities.h"
// UE5.5 correct includes
#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"
#include "UObject/Field.h"
#include "UObject/FieldPath.h"
// Blueprint Graph specific includes
#include "EdGraph/EdGraph.h"
#include "EdGraph/EdGraphNode.h"
#include "EdGraph/EdGraphPin.h"
#include "K2Node_CallFunction.h"
#include "K2Node_InputAction.h"
#include "K2Node_Self.h"
#include "GameFramework/InputSettings.h"
#include "EditorSubsystem.h"
#include "Subsystems/EditorActorSubsystem.h"
// Include our new command handler classes
#include "Commands/UnrealMCPEditorCommands.h"
#include "Commands/UnrealMCPBlueprintCommands.h"
#include "Commands/UnrealMCPBlueprintNodeCommands.h"
#include "Commands/UnrealMCPProjectCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Commands/UnrealMCPUMGCommands.h"

// Default settings
#define MCP_SERVER_HOST "127.0.0.1"
#define MCP_SERVER_PORT 55557

UUnrealMCPBridge::UUnrealMCPBridge()
{
    EditorCommands = MakeShared<FUnrealMCPEditorCommands>();
    BlueprintCommands = MakeShared<FUnrealMCPBlueprintCommands>();
    BlueprintNodeCommands = MakeShared<FUnrealMCPBlueprintNodeCommands>();
    ProjectCommands = MakeShared<FUnrealMCPProjectCommands>();
    UMGCommands = MakeShared<FUnrealMCPUMGCommands>();
    WorldPartitionCommands = MakeShared<FUnrealMCPWorldPartitionCommands>();
    CollisionCommands = MakeShared<FUnrealMCPCollisionCommands>();
    PathfindingCommands = MakeShared<FUnrealMCPPathfindingCommands>();
    VisionCommands = MakeShared<FUnrealMCPVisionCommands>();
    AICommands = MakeShared<FUnrealMCPAICommands>();
    RealmCommands = MakeShared<FUnrealMCPRealmCommands>();
    NetworkCommands = MakeShared<FUnrealMCPNetworkCommands>();
    ProceduralCommands = MakeShared<FUnrealMCPProceduralCommands>();
    PerformanceCommands = MakeShared<FUnrealMCPPerformanceCommands>();
}

UUnrealMCPBridge::~UUnrealMCPBridge()
{
    EditorCommands.Reset();
    BlueprintCommands.Reset();
    BlueprintNodeCommands.Reset();
    ProjectCommands.Reset();
    UMGCommands.Reset();
    WorldPartitionCommands.Reset();
    CollisionCommands.Reset();
    PathfindingCommands.Reset();
    VisionCommands.Reset();
    AICommands.Reset();
    RealmCommands.Reset();
    NetworkCommands.Reset();
    ProceduralCommands.Reset();
    PerformanceCommands.Reset();
}

// Initialize subsystem
void UUnrealMCPBridge::Initialize(FSubsystemCollectionBase& Collection)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Initializing"));
    
    bIsRunning = false;
    ListenerSocket = nullptr;
    ConnectionSocket = nullptr;
    ServerThread = nullptr;
    Port = MCP_SERVER_PORT;
    FIPv4Address::Parse(MCP_SERVER_HOST, ServerAddress);

    // Start the server automatically
    StartServer();
}

// Clean up resources when subsystem is destroyed
void UUnrealMCPBridge::Deinitialize()
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Shutting down"));
    StopServer();
}

// Start the MCP server
void UUnrealMCPBridge::StartServer()
{
    if (bIsRunning)
    {
        UE_LOG(LogTemp, Warning, TEXT("UnrealMCPBridge: Server is already running"));
        return;
    }

    // Create socket subsystem
    ISocketSubsystem* SocketSubsystem = ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM);
    if (!SocketSubsystem)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to get socket subsystem"));
        return;
    }

    // Create listener socket
    TSharedPtr<FSocket> NewListenerSocket = MakeShareable(SocketSubsystem->CreateSocket(NAME_Stream, TEXT("UnrealMCPListener"), false));
    if (!NewListenerSocket.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create listener socket"));
        return;
    }

    // Allow address reuse for quick restarts
    NewListenerSocket->SetReuseAddr(true);
    NewListenerSocket->SetNonBlocking(true);

    // Bind to address
    FIPv4Endpoint Endpoint(ServerAddress, Port);
    if (!NewListenerSocket->Bind(*Endpoint.ToInternetAddr()))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to bind listener socket to %s:%d"), *ServerAddress.ToString(), Port);
        return;
    }

    // Start listening
    if (!NewListenerSocket->Listen(5))
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to start listening"));
        return;
    }

    ListenerSocket = NewListenerSocket;
    bIsRunning = true;
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server started on %s:%d"), *ServerAddress.ToString(), Port);

    // Start server thread
    ServerThread = FRunnableThread::Create(
        new FMCPServerRunnable(this, ListenerSocket),
        TEXT("UnrealMCPServerThread"),
        0, TPri_Normal
    );

    if (!ServerThread)
    {
        UE_LOG(LogTemp, Error, TEXT("UnrealMCPBridge: Failed to create server thread"));
        StopServer();
        return;
    }
}

// Stop the MCP server
void UUnrealMCPBridge::StopServer()
{
    if (!bIsRunning)
    {
        return;
    }

    bIsRunning = false;

    // Clean up thread
    if (ServerThread)
    {
        ServerThread->Kill(true);
        delete ServerThread;
        ServerThread = nullptr;
    }

    // Close sockets
    if (ConnectionSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ConnectionSocket.Get());
        ConnectionSocket.Reset();
    }

    if (ListenerSocket.IsValid())
    {
        ISocketSubsystem::Get(PLATFORM_SOCKETSUBSYSTEM)->DestroySocket(ListenerSocket.Get());
        ListenerSocket.Reset();
    }

    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Server stopped"));
}

// Execute a command received from a client
FString UUnrealMCPBridge::ExecuteCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    UE_LOG(LogTemp, Display, TEXT("UnrealMCPBridge: Executing command: %s"), *CommandType);
    
    // Create a promise to wait for the result
    TPromise<FString> Promise;
    TFuture<FString> Future = Promise.GetFuture();
    
    // Queue execution on Game Thread
    AsyncTask(ENamedThreads::GameThread, [this, CommandType, Params, Promise = MoveTemp(Promise)]() mutable
    {
        TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
        
        try
        {
            TSharedPtr<FJsonObject> ResultJson;
            
            if (CommandType == TEXT("ping"))
            {
                ResultJson = MakeShareable(new FJsonObject);
                ResultJson->SetStringField(TEXT("message"), TEXT("pong"));
            }
            // Editor Commands (including actor manipulation)
            else if (CommandType == TEXT("get_actors_in_level") || 
                     CommandType == TEXT("find_actors_by_name") ||
                     CommandType == TEXT("spawn_actor") ||
                     CommandType == TEXT("create_actor") ||
                     CommandType == TEXT("delete_actor") || 
                     CommandType == TEXT("set_actor_transform") ||
                     CommandType == TEXT("get_actor_properties") ||
                     CommandType == TEXT("set_actor_property") ||
                     CommandType == TEXT("spawn_blueprint_actor") ||
                     CommandType == TEXT("focus_viewport") || 
                     CommandType == TEXT("take_screenshot"))
            {
                ResultJson = EditorCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Commands
            else if (CommandType == TEXT("create_blueprint") || 
                     CommandType == TEXT("add_component_to_blueprint") || 
                     CommandType == TEXT("set_component_property") || 
                     CommandType == TEXT("set_physics_properties") || 
                     CommandType == TEXT("compile_blueprint") || 
                     CommandType == TEXT("set_blueprint_property") || 
                     CommandType == TEXT("set_static_mesh_properties") ||
                     CommandType == TEXT("set_pawn_properties"))
            {
                ResultJson = BlueprintCommands->HandleCommand(CommandType, Params);
            }
            // Blueprint Node Commands
            else if (CommandType == TEXT("connect_blueprint_nodes") || 
                     CommandType == TEXT("add_blueprint_get_self_component_reference") ||
                     CommandType == TEXT("add_blueprint_self_reference") ||
                     CommandType == TEXT("find_blueprint_nodes") ||
                     CommandType == TEXT("add_blueprint_event_node") ||
                     CommandType == TEXT("add_blueprint_input_action_node") ||
                     CommandType == TEXT("add_blueprint_function_node") ||
                     CommandType == TEXT("add_blueprint_get_component_node") ||
                     CommandType == TEXT("add_blueprint_variable"))
            {
                ResultJson = BlueprintNodeCommands->HandleCommand(CommandType, Params);
            }
            // Project Commands
            else if (CommandType == TEXT("create_input_mapping"))
            {
                ResultJson = ProjectCommands->HandleCommand(CommandType, Params);
            }
            // UMG Commands
            else if (CommandType == TEXT("create_umg_widget_blueprint") ||
                     CommandType == TEXT("add_text_block_to_widget") ||
                     CommandType == TEXT("add_button_to_widget") ||
                     CommandType == TEXT("bind_widget_event") ||
                     CommandType == TEXT("set_text_block_binding") ||
                     CommandType == TEXT("add_widget_to_viewport"))
            {
                ResultJson = UMGCommands->HandleCommand(CommandType, Params);
            }
            // World Partition Commands
            else if (CommandType == TEXT("create_world_partition_level") ||
                     CommandType == TEXT("configure_streaming_cell") ||
                     CommandType == TEXT("create_layer_hierarchy") ||
                     CommandType == TEXT("set_spatial_division_rules") ||
                     CommandType == TEXT("configure_layer_streaming_manager") ||
                     CommandType == TEXT("get_world_partition_status") ||
                     CommandType == TEXT("optimize_streaming_cells"))
            {
                ResultJson = WorldPartitionCommands->HandleCommand(CommandType, Params);
            }
            // Collision Commands
            else if (CommandType == TEXT("create_collision_channel") ||
                     CommandType == TEXT("configure_collision_profile") ||
                     CommandType == TEXT("set_layer_collision_rules") ||
                     CommandType == TEXT("configure_collision_size_scaling") ||
                     CommandType == TEXT("create_layer_collision_matrix") ||
                     CommandType == TEXT("optimize_collision_detection") ||
                     CommandType == TEXT("configure_collision_complexity") ||
                     CommandType == TEXT("setup_layer_collision_filtering") ||
                     CommandType == TEXT("get_collision_system_status"))
            {
                ResultJson = CollisionCommands->HandleCommand(CommandType, Params);
            }
            // Pathfinding Commands
            else if (CommandType == TEXT("create_navigation_mesh_layer") ||
                     CommandType == TEXT("configure_astar_algorithm") ||
                     CommandType == TEXT("set_movement_costs") ||
                     CommandType == TEXT("create_layer_connections") ||
                     CommandType == TEXT("configure_pathfinding_constraints") ||
                     CommandType == TEXT("create_dynamic_obstacles") ||
                     CommandType == TEXT("find_path_multilayer") ||
                     CommandType == TEXT("optimize_navigation_performance") ||
                     CommandType == TEXT("configure_hierarchical_pathfinding") ||
                     CommandType == TEXT("setup_crowd_navigation") ||
                     CommandType == TEXT("debug_navigation_layer") ||
                     CommandType == TEXT("validate_navigation_setup") ||
                     CommandType == TEXT("get_pathfinding_system_status"))
            {
                ResultJson = PathfindingCommands->HandleCommand(CommandType, Params);
            }
            // Vision Commands
            else if (CommandType == TEXT("create_fog_of_war_layer") ||
                     CommandType == TEXT("configure_vision_range_layer") ||
                     CommandType == TEXT("setup_line_of_sight_system") ||
                     CommandType == TEXT("create_vision_blocking_volumes") ||
                     CommandType == TEXT("configure_dynamic_fog_updates") ||
                     CommandType == TEXT("setup_multilayer_vision_interactions") ||
                     CommandType == TEXT("create_vision_sensors") ||
                     CommandType == TEXT("configure_vision_occlusion_system") ||
                     CommandType == TEXT("setup_fog_of_war_persistence") ||
                     CommandType == TEXT("calculate_vision_coverage") ||
                     CommandType == TEXT("optimize_vision_performance") ||
                     CommandType == TEXT("debug_vision_system") ||
                     CommandType == TEXT("validate_vision_setup") ||
                     CommandType == TEXT("get_vision_system_status"))
            {
                ResultJson = VisionCommands->HandleCommand(CommandType, Params);
            }
            // AI Commands
            else if (CommandType == TEXT("create_ai_learning_pipeline") ||
                     CommandType == TEXT("configure_adaptive_behavior") ||
                     CommandType == TEXT("setup_dynamic_spawn_system") ||
                     CommandType == TEXT("create_ai_decision_tree") ||
                     CommandType == TEXT("configure_special_events") ||
                     CommandType == TEXT("setup_ai_communication_system") ||
                     CommandType == TEXT("configure_player_profiling") ||
                     CommandType == TEXT("setup_ai_memory_system") ||
                     CommandType == TEXT("optimize_ai_performance") ||
                     CommandType == TEXT("debug_ai_system") ||
                     CommandType == TEXT("validate_ai_setup") ||
                     CommandType == TEXT("get_ai_system_status"))
            {
                ResultJson = AICommands->HandleCommand(CommandType, Params);
            }
            // Realm Commands
            else if (CommandType == TEXT("create_realm_transition_system") ||
                     CommandType == TEXT("configure_asset_streaming") ||
                     CommandType == TEXT("setup_world_partitioning") ||
                     CommandType == TEXT("create_transition_triggers") ||
                     CommandType == TEXT("configure_realm_persistence") ||
                     CommandType == TEXT("setup_cross_realm_communication") ||
                     CommandType == TEXT("optimize_transition_performance") ||
                     CommandType == TEXT("debug_realm_transitions") ||
                     CommandType == TEXT("validate_realm_setup") ||
                     CommandType == TEXT("get_realm_system_status") ||
                     // Analytics and Telemetry Commands
                     CommandType == TEXT("start_analytics_collection") ||
                     CommandType == TEXT("stop_analytics_collection") ||
                     CommandType == TEXT("collect_gameplay_metrics") ||
                     CommandType == TEXT("process_analytics_data") ||
                     CommandType == TEXT("generate_analytics_visualization") ||
                     CommandType == TEXT("configure_metric_alerts") ||
                     CommandType == TEXT("export_analytics_data") ||
                     CommandType == TEXT("import_analytics_data") ||
                     CommandType == TEXT("get_performance_statistics") ||
                     CommandType == TEXT("setup_custom_telemetry") ||
                     CommandType == TEXT("generate_player_behavior_report") ||
                     CommandType == TEXT("create_realtime_dashboard") ||
                     CommandType == TEXT("aggregate_multilayer_data") ||
                     CommandType == TEXT("run_predictive_analysis") ||
                     CommandType == TEXT("setup_ab_testing") ||
                     CommandType == TEXT("generate_heatmap_analysis"))
            {
                ResultJson = RealmCommands->HandleCommand(CommandType, Params);
            }
            // Network Commands
            else if (CommandType == TEXT("create_multilayer_network_system") ||
                     CommandType == TEXT("configure_object_replication") ||
                     CommandType == TEXT("setup_client_prediction") ||
                     CommandType == TEXT("configure_network_synchronization") ||
                     CommandType == TEXT("setup_lag_compensation") ||
                     CommandType == TEXT("configure_bandwidth_optimization") ||
                     CommandType == TEXT("debug_network_performance") ||
                     CommandType == TEXT("validate_network_setup") ||
                     CommandType == TEXT("get_network_system_status"))
            {
                ResultJson = NetworkCommands->HandleCommand(CommandType, Params);
            }
            // Procedural Commands
            else if (CommandType == TEXT("create_procedural_generation_system") ||
                     CommandType == TEXT("generate_dynamic_objectives") ||
                     CommandType == TEXT("configure_dynamic_balancing") ||
                     CommandType == TEXT("setup_content_generation") ||
                     CommandType == TEXT("configure_reward_scaling") ||
                     CommandType == TEXT("optimize_generation_performance") ||
                     CommandType == TEXT("debug_generation_system") ||
                     CommandType == TEXT("validate_generation_setup") ||
                     CommandType == TEXT("get_generation_system_status"))
            {
                ResultJson = ProceduralCommands->HandleCommand(CommandType, Params);
            }
            // Performance Commands
            else if (CommandType == TEXT("create_performance_optimization_system") ||
                     CommandType == TEXT("configure_dynamic_lod") ||
                     CommandType == TEXT("setup_culling_systems") ||
                     CommandType == TEXT("configure_memory_management") ||
                     CommandType == TEXT("optimize_rendering_pipeline") ||
                     CommandType == TEXT("monitor_performance_metrics") ||
                     CommandType == TEXT("debug_performance_issues") ||
                     CommandType == TEXT("validate_performance_setup") ||
                     CommandType == TEXT("get_performance_system_status"))
            {
                ResultJson = PerformanceCommands->HandleCommand(CommandType, Params);
            }
            else
            {
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), FString::Printf(TEXT("Unknown command: %s"), *CommandType));
                
                FString ResultString;
                TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
                FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
                Promise.SetValue(ResultString);
                return;
            }
            
            // Check if the result contains an error
            bool bSuccess = true;
            FString ErrorMessage;
            
            if (ResultJson->HasField(TEXT("success")))
            {
                bSuccess = ResultJson->GetBoolField(TEXT("success"));
                if (!bSuccess && ResultJson->HasField(TEXT("error")))
                {
                    ErrorMessage = ResultJson->GetStringField(TEXT("error"));
                }
            }
            
            if (bSuccess)
            {
                // Set success status and include the result
                ResponseJson->SetStringField(TEXT("status"), TEXT("success"));
                ResponseJson->SetObjectField(TEXT("result"), ResultJson);
            }
            else
            {
                // Set error status and include the error message
                ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
                ResponseJson->SetStringField(TEXT("error"), ErrorMessage);
            }
        }
        catch (const std::exception& e)
        {
            ResponseJson->SetStringField(TEXT("status"), TEXT("error"));
            ResponseJson->SetStringField(TEXT("error"), UTF8_TO_TCHAR(e.what()));
        }
        
        FString ResultString;
        TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResultString);
        FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);
        Promise.SetValue(ResultString);
    });
    
    return Future.Get();
}