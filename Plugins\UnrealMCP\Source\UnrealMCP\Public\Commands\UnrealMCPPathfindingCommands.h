#pragma once

#include "CoreMinimal.h"
#include "Dom/JsonObject.h"
#include "Engine/World.h"
#include "NavigationSystem.h"
#include "NavMesh/RecastNavMesh.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationData.h"
#include "NavModifierVolume.h"
#include "NavAreas/NavArea_Obstacle.h"
#include "NavAreas/NavArea_Null.h"

/**
 * Classe responsável por gerenciar comandos relacionados ao Sistema de Pathfinding A* Multicamada.
 * Implementa navegação 3D com múltiplas camadas, algoritmos A* otimizados e estruturas de dados
 * especializadas para pathfinding em ambientes complexos.
 */
class UNREALMCP_API FUnrealMCPPathfindingCommands
{
public:
    FUnrealMCPPathfindingCommands();
    ~FUnrealMCPPathfindingCommands();

    /**
     * Processa comandos relacionados ao sistema de pathfinding multicamada.
     * @param CommandType Tipo do comando a ser executado
     * @param Params Parâmetros do comando em formato JSON
     * @return Resposta do comando em formato JSON
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // === Gerenciamento de Camadas de Navegação ===
    
    /**
     * Cria uma nova camada de navigation mesh para pathfinding 3D.
     * @param Params Parâmetros incluindo nome da camada, altura, configurações de célula
     * @return Resposta com informações da camada criada
     */
    TSharedPtr<FJsonObject> HandleCreateNavigationMeshLayer(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Configura o algoritmo A* para uma camada específica.
     * @param Params Parâmetros de configuração do algoritmo (heurística, pesos, limites)
     * @return Resposta com configuração aplicada
     */
    TSharedPtr<FJsonObject> HandleConfigureAStarAlgorithm(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Define custos de movimento para diferentes tipos de terreno e transições.
     * @param Params Custos por tipo de terreno e transições entre camadas
     * @return Resposta com configuração dos custos
     */
    TSharedPtr<FJsonObject> HandleSetMovementCosts(const TSharedPtr<FJsonObject>& Params);
    
    // === Conexões Entre Camadas ===
    
    /**
     * Cria conexões entre camadas de navegação para pathfinding 3D.
     * @param Params Camadas origem/destino, pontos de conexão, tipo de conexão
     * @return Resposta com informações das conexões criadas
     */
    TSharedPtr<FJsonObject> HandleCreateLayerConnections(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Configura restrições e otimizações para o pathfinding.
     * @param Params Limites de busca, otimizações de caminho, configurações de performance
     * @return Resposta com configuração das restrições
     */
    TSharedPtr<FJsonObject> HandleConfigurePathfindingConstraints(const TSharedPtr<FJsonObject>& Params);
    
    // === Obstáculos Dinâmicos ===
    
    /**
     * Cria obstáculos dinâmicos que afetam o pathfinding.
     * @param Params Dados dos obstáculos (posição, tamanho, tipo, comportamento)
     * @return Resposta com informações dos obstáculos criados
     */
    TSharedPtr<FJsonObject> HandleCreateDynamicObstacles(const TSharedPtr<FJsonObject>& Params);
    
    // === Busca de Caminhos ===
    
    /**
     * Encontra um caminho através de múltiplas camadas usando A*.
     * @param Params Posições inicial/final, propriedades do agente, camadas permitidas
     * @return Resposta com o caminho encontrado e informações de navegação
     */
    TSharedPtr<FJsonObject> HandleFindPathMultilayer(const TSharedPtr<FJsonObject>& Params);
    
    // === Otimização e Performance ===
    
    /**
     * Otimiza o desempenho do sistema de navegação.
     * @param Params Configurações de otimização (cache, pré-computação, LOD)
     * @return Resposta com resultados da otimização
     */
    TSharedPtr<FJsonObject> HandleOptimizeNavigationPerformance(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Configura pathfinding hierárquico para melhor performance.
     * @param Params Configurações de hierarquia (clusters, níveis, pré-computação)
     * @return Resposta com configuração hierárquica
     */
    TSharedPtr<FJsonObject> HandleConfigureHierarchicalPathfinding(const TSharedPtr<FJsonObject>& Params);
    
    // === Navegação de Multidões ===
    
    /**
     * Configura navegação para multidões com evitamento de colisões.
     * @param Params Configurações para navegação de multidões
     * @return Resposta com configuração de multidões
     */
    TSharedPtr<FJsonObject> HandleSetupCrowdNavigation(const TSharedPtr<FJsonObject>& Params);
    
    // === Debug e Validação ===
    
    /**
     * Ativa visualização de debug para uma camada de navegação.
     * @param Params Configurações de visualização (navmesh, conexões, obstáculos)
     * @return Resposta com configuração de debug
     */
    TSharedPtr<FJsonObject> HandleDebugNavigationLayer(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Valida a configuração do sistema de navegação multicamada.
     * @param Params Lista de camadas para validar
     * @return Resposta com resultados da validação
     */
    TSharedPtr<FJsonObject> HandleValidateNavigationSetup(const TSharedPtr<FJsonObject>& Params);
    
    /**
     * Obtém o status do sistema de pathfinding multicamada.
     * @param Params Opções para incluir métricas de performance
     * @return Resposta com status do sistema
     */
    TSharedPtr<FJsonObject> HandleGetPathfindingSystemStatus(const TSharedPtr<FJsonObject>& Params);
    
    // === Funções Auxiliares ===
    
    /**
     * Converte string para tipo de heurística A*.
     * @param HeuristicString String representando o tipo de heurística
     * @return Enum correspondente ao tipo de heurística
     */
    int32 StringToHeuristicType(const FString& HeuristicString);
    
    /**
     * Converte tipo de heurística para string.
     * @param HeuristicType Tipo de heurística
     * @return String representando o tipo
     */
    FString HeuristicTypeToString(int32 HeuristicType);
    
    /**
     * Converte string para tipo de conexão entre camadas.
     * @param ConnectionString String representando o tipo de conexão
     * @return Enum correspondente ao tipo de conexão
     */
    int32 StringToConnectionType(const FString& ConnectionString);
    
    /**
     * Converte tipo de conexão para string.
     * @param ConnectionType Tipo de conexão
     * @return String representando o tipo
     */
    FString ConnectionTypeToString(int32 ConnectionType);
    
    /**
     * Converte string para tipo de otimização de caminho.
     * @param OptimizationString String representando o tipo de otimização
     * @return Enum correspondente ao tipo de otimização
     */
    int32 StringToPathOptimizationType(const FString& OptimizationString);
    
    /**
     * Converte tipo de otimização para string.
     * @param OptimizationType Tipo de otimização
     * @return String representando o tipo
     */
    FString PathOptimizationTypeToString(int32 OptimizationType);
    
    // === Implementações Específicas ===
    
    /**
     * Cria uma camada de navigation mesh customizada.
     * @param LayerName Nome da camada
     * @param LayerHeight Altura da camada no mundo
     * @param NavMeshSettings Configurações do navmesh
     * @return Sucesso da operação
     */
    bool CreateCustomNavigationLayer(const FString& LayerName, float LayerHeight, const TSharedPtr<FJsonObject>& NavMeshSettings);
    
    /**
     * Configura algoritmo A* para uma camada.
     * @param LayerName Nome da camada
     * @param AlgorithmSettings Configurações do algoritmo
     * @return Sucesso da operação
     */
    bool ConfigureLayerAStarAlgorithm(const FString& LayerName, const TSharedPtr<FJsonObject>& AlgorithmSettings);
    
    /**
     * Aplica custos de movimento a uma camada.
     * @param LayerName Nome da camada
     * @param CostSettings Configurações de custos
     * @return Sucesso da operação
     */
    bool ApplyMovementCostsToLayer(const FString& LayerName, const TSharedPtr<FJsonObject>& CostSettings);
    
    /**
     * Estabelece conexões entre camadas de navegação.
     * @param SourceLayer Camada de origem
     * @param TargetLayer Camada de destino
     * @param ConnectionData Dados da conexão
     * @return Sucesso da operação
     */
    bool EstablishLayerConnections(const FString& SourceLayer, const FString& TargetLayer, const TSharedPtr<FJsonObject>& ConnectionData);
    
    /**
     * Executa busca de caminho A* multicamada.
     * @param StartPos Posição inicial
     * @param EndPos Posição final
     * @param AgentProperties Propriedades do agente
     * @param AllowedLayers Camadas permitidas
     * @return Dados do caminho encontrado
     */
    TSharedPtr<FJsonObject> ExecuteMultilayerPathfinding(const FVector& StartPos, const FVector& EndPos, 
                                                        const TSharedPtr<FJsonObject>& AgentProperties,
                                                        const TArray<FString>& AllowedLayers);
    
    /**
     * Otimiza performance do sistema de navegação.
     * @param LayerName Nome da camada
     * @param OptimizationSettings Configurações de otimização
     * @return Sucesso da operação
     */
    bool OptimizeNavigationSystem(const FString& LayerName, const TSharedPtr<FJsonObject>& OptimizationSettings);
    
    /**
     * Configura sistema hierárquico de pathfinding.
     * @param LayerName Nome da camada
     * @param HierarchySettings Configurações hierárquicas
     * @return Sucesso da operação
     */
    bool SetupHierarchicalSystem(const FString& LayerName, const TSharedPtr<FJsonObject>& HierarchySettings);
    
    // === Validação ===
    
    /**
     * Valida nome de camada de navegação.
     * @param LayerName Nome da camada
     * @return Verdadeiro se válido
     */
    bool ValidateNavigationLayerName(const FString& LayerName);
    
    /**
     * Valida configurações de agente.
     * @param AgentProperties Propriedades do agente
     * @return Verdadeiro se válido
     */
    bool ValidateAgentProperties(const TSharedPtr<FJsonObject>& AgentProperties);
    
    /**
     * Valida posição no mundo.
     * @param Position Posição a validar
     * @return Verdadeiro se válido
     */
    bool ValidateWorldPosition(const FVector& Position);
    
    /**
     * Encontra um caminho entre camadas de navegação.
     * @param StartLayer Camada inicial
     * @param EndLayer Camada final
     * @param AllowedLayers Camadas permitidas para navegação
     * @param OutLayerPath Caminho de camadas encontrado
     * @return Verdadeiro se um caminho foi encontrado
     */
    bool FindLayerPath(const FString& StartLayer, const FString& EndLayer, 
                      const TArray<FString>& AllowedLayers, TArray<FString>& OutLayerPath);
    
    /**
     * Encontra a melhor conexão entre duas camadas.
     * @param SourceLayer Camada de origem
     * @param TargetLayer Camada de destino
     * @param CurrentPosition Posição atual do agente
     * @param OutConnectionStart Posição de início da conexão
     * @param OutConnectionEnd Posição de fim da conexão
     * @return Verdadeiro se uma conexão foi encontrada
     */
    bool FindBestConnection(const FString& SourceLayer, const FString& TargetLayer,
                           const FVector& CurrentPosition, FVector& OutConnectionStart, FVector& OutConnectionEnd);
    
    // === Utilitários do Sistema ===
    
    /**
     * Obtém o sistema de navegação atual do mundo.
     * @param World Ponteiro para o mundo (opcional, usa GWorld se não fornecido)
     * @return Ponteiro para o NavigationSystem ou nullptr se não disponível
     */
    UNavigationSystemV1* GetNavigationSystem(UWorld* World = nullptr);
    
    // === Utilitários de Resposta ===
    
    /**
     * Cria resposta de erro.
     * @param ErrorMessage Mensagem de erro
     * @return Objeto JSON de resposta de erro
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage);
    
    /**
     * Cria resposta de sucesso.
     * @param Data Dados da resposta
     * @return Objeto JSON de resposta de sucesso
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data);
    
    // === Membros Privados ===
    
    /** Cache de camadas de navegação criadas */
    TMap<FString, TWeakObjectPtr<ANavigationData>> NavigationLayers;
    
    /** Configurações de algoritmos A* por camada */
    TMap<FString, TSharedPtr<FJsonObject>> LayerAlgorithmConfigs;
    
    /** Configurações de custos de movimento por camada */
    TMap<FString, TSharedPtr<FJsonObject>> LayerCostSettings;
    
    /** Conexões entre camadas */
    TMap<FString, TArray<TSharedPtr<FJsonObject>>> LayerConnections;
    
    /** Obstáculos dinâmicos criados */
    TMap<FString, TWeakObjectPtr<ANavModifierVolume>> DynamicObstacles;
    
    /** Sistema de navegação ativo */
    TWeakObjectPtr<UNavigationSystemV1> NavigationSystem;
};