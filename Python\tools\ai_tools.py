"""AI Tools for Unreal MCP.

This module provides tools for creating and managing adaptive AI systems in Unreal Engine.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_ai_tools(mcp: FastMCP):
    """Register AI tools with the MCP server."""
    
    @mcp.tool()
    def create_ai_learning_pipeline(
        ctx: Context,
        layer_name: str,
        learning_type: str = "reinforcement",
        model_config: Dict[str, Any] = None,
        training_data_path: str = ""
    ) -> Dict[str, Any]:
        """
        Cria um pipeline de machine learning para IA adaptativa.
        
        Args:
            layer_name: Nome da camada de IA
            learning_type: Tipo de aprendizado (reinforcement, supervised, unsupervised)
            model_config: Configuração do modelo de ML
            training_data_path: Caminho para dados de treinamento
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if model_config is None:
                model_config = {
                    "type": "neural_network",
                    "hidden_layers": [128, 64, 32],
                    "activation_function": "relu",
                    "optimizer": "adam",
                    "learning_rate": 0.001
                }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "learning_type": learning_type,
                "model_config": model_config,
                "training_data_path": training_data_path
            }
            
            logger.info(f"Creating AI learning pipeline with params: {command_data}")
            response = unreal.send_command("create_ai_learning_pipeline", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI learning pipeline creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating AI learning pipeline: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_adaptive_behavior(
        ctx: Context,
        layer_name: str,
        behavior_type: str,
        adaptation_rules: List[Dict[str, Any]] = None,
        response_threshold: float = 0.7
    ) -> Dict[str, Any]:
        """
        Configura comportamentos adaptativos para NPCs.
        
        Args:
            layer_name: Nome da camada de IA
            behavior_type: Tipo de comportamento (aggressive, defensive, neutral, adaptive)
            adaptation_rules: Regras de adaptação baseadas em contexto
            response_threshold: Limiar para mudança de comportamento
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if adaptation_rules is None:
                adaptation_rules = [
                    {"trigger": "player_proximity", "distance": 500.0, "response": "alert"},
                    {"trigger": "health_low", "threshold": 0.3, "response": "retreat"},
                    {"trigger": "ally_death", "radius": 1000.0, "response": "aggressive"}
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "behavior_type": behavior_type,
                "adaptation_rules": adaptation_rules,
                "response_threshold": response_threshold
            }
            
            logger.info(f"Configuring adaptive behavior with params: {command_data}")
            response = unreal.send_command("configure_adaptive_behavior", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Adaptive behavior configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring adaptive behavior: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_dynamic_spawn_system(
        ctx: Context,
        layer_name: str,
        spawn_rules: List[Dict[str, Any]] = None,
        difficulty_scaling: Dict[str, Any] = None,
        population_limits: Dict[str, int] = None
    ) -> Dict[str, Any]:
        """
        Configura sistema de spawn dinâmico baseado em IA.
        
        Args:
            layer_name: Nome da camada de spawn
            spawn_rules: Regras de spawn adaptativo
            difficulty_scaling: Configuração de escalonamento de dificuldade
            population_limits: Limites de população por tipo de NPC
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if spawn_rules is None:
                spawn_rules = [
                    {"condition": "player_level", "min_value": 1, "max_value": 10, "spawn_rate": 1.0},
                    {"condition": "area_cleared", "cooldown": 300.0, "spawn_rate": 0.5},
                    {"condition": "player_performance", "metric": "kill_rate", "spawn_rate": 1.5}
                ]
            
            if difficulty_scaling is None:
                difficulty_scaling = {
                    "base_difficulty": 1.0,
                    "scaling_factor": 0.1,
                    "max_difficulty": 3.0,
                    "adaptation_speed": 0.05
                }
            
            if population_limits is None:
                population_limits = {
                    "basic_enemy": 20,
                    "elite_enemy": 5,
                    "boss_enemy": 1,
                    "neutral_npc": 15
                }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "spawn_rules": spawn_rules,
                "difficulty_scaling": difficulty_scaling,
                "population_limits": population_limits
            }
            
            logger.info(f"Setting up dynamic spawn system with params: {command_data}")
            response = unreal.send_command("setup_dynamic_spawn_system", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Dynamic spawn system setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up dynamic spawn system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_ai_decision_tree(
        ctx: Context,
        layer_name: str,
        tree_name: str,
        decision_nodes: List[Dict[str, Any]] = None,
        learning_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Cria árvore de decisão para IA adaptativa.
        
        Args:
            layer_name: Nome da camada de IA
            tree_name: Nome da árvore de decisão
            decision_nodes: Nós da árvore de decisão
            learning_enabled: Se a árvore deve aprender e se adaptar
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if decision_nodes is None:
                decision_nodes = [
                    {
                        "id": "root",
                        "type": "condition",
                        "condition": "player_detected",
                        "true_branch": "combat_node",
                        "false_branch": "patrol_node"
                    },
                    {
                        "id": "combat_node",
                        "type": "action",
                        "action": "engage_combat",
                        "parameters": {"aggression": 0.8}
                    },
                    {
                        "id": "patrol_node",
                        "type": "action",
                        "action": "patrol_area",
                        "parameters": {"speed": 0.5}
                    }
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "tree_name": tree_name,
                "decision_nodes": decision_nodes,
                "learning_enabled": learning_enabled
            }
            
            logger.info(f"Creating AI decision tree with params: {command_data}")
            response = unreal.send_command("create_ai_decision_tree", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI decision tree creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating AI decision tree: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_special_events(
        ctx: Context,
        layer_name: str,
        event_triggers: List[Dict[str, Any]] = None,
        event_responses: List[Dict[str, Any]] = None,
        adaptive_scaling: bool = True
    ) -> Dict[str, Any]:
        """
        Configura eventos especiais adaptativos.
        
        Args:
            layer_name: Nome da camada de eventos
            event_triggers: Gatilhos para eventos especiais
            event_responses: Respostas aos eventos
            adaptive_scaling: Se os eventos devem escalar adaptativamente
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if event_triggers is None:
                event_triggers = [
                    {"type": "boss_spawn", "condition": "area_boss_defeated", "cooldown": 1800.0},
                    {"type": "treasure_spawn", "condition": "exploration_milestone", "probability": 0.3},
                    {"type": "ambush_event", "condition": "player_overconfidence", "difficulty_modifier": 1.5}
                ]
            
            if event_responses is None:
                event_responses = [
                    {"event": "boss_spawn", "action": "spawn_boss", "location": "dynamic"},
                    {"event": "treasure_spawn", "action": "create_treasure", "rarity": "adaptive"},
                    {"event": "ambush_event", "action": "spawn_ambush", "intensity": "scaled"}
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "event_triggers": event_triggers,
                "event_responses": event_responses,
                "adaptive_scaling": adaptive_scaling
            }
            
            logger.info(f"Configuring special events with params: {command_data}")
            response = unreal.send_command("configure_special_events", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Special events configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring special events: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_ai_communication_system(
        ctx: Context,
        layer_name: str,
        communication_range: float = 1000.0,
        message_types: List[str] = None,
        learning_from_communication: bool = True
    ) -> Dict[str, Any]:
        """
        Configura sistema de comunicação entre NPCs.
        
        Args:
            layer_name: Nome da camada de comunicação
            communication_range: Alcance da comunicação
            message_types: Tipos de mensagens disponíveis
            learning_from_communication: Se NPCs aprendem através da comunicação
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if message_types is None:
                message_types = [
                    "alert_player_spotted",
                    "request_backup",
                    "share_patrol_route",
                    "report_player_behavior",
                    "coordinate_attack"
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "communication_range": communication_range,
                "message_types": message_types,
                "learning_from_communication": learning_from_communication
            }
            
            logger.info(f"Setting up AI communication system with params: {command_data}")
            response = unreal.send_command("setup_ai_communication_system", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI communication system setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up AI communication system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_player_profiling(
        ctx: Context,
        layer_name: str,
        profiling_metrics: List[str] = None,
        adaptation_speed: float = 0.1,
        profile_persistence: bool = True
    ) -> Dict[str, Any]:
        """
        Configura sistema de profiling do jogador para adaptação da IA.
        
        Args:
            layer_name: Nome da camada de profiling
            profiling_metrics: Métricas para análise do jogador
            adaptation_speed: Velocidade de adaptação baseada no perfil
            profile_persistence: Se o perfil persiste entre sessões
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if profiling_metrics is None:
                profiling_metrics = [
                    "combat_style",
                    "exploration_pattern",
                    "risk_tolerance",
                    "reaction_time",
                    "preferred_strategies",
                    "skill_progression"
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "profiling_metrics": profiling_metrics,
                "adaptation_speed": adaptation_speed,
                "profile_persistence": profile_persistence
            }
            
            logger.info(f"Configuring player profiling with params: {command_data}")
            response = unreal.send_command("configure_player_profiling", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Player profiling configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring player profiling: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_ai_memory_system(
        ctx: Context,
        layer_name: str,
        memory_types: List[str] = None,
        memory_duration: Dict[str, float] = None,
        shared_memory: bool = True
    ) -> Dict[str, Any]:
        """
        Configura sistema de memória para NPCs.
        
        Args:
            layer_name: Nome da camada de memória
            memory_types: Tipos de memória disponíveis
            memory_duration: Duração de cada tipo de memória
            shared_memory: Se NPCs compartilham memórias
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if memory_types is None:
                memory_types = [
                    "player_last_seen",
                    "combat_experience",
                    "patrol_routes",
                    "threat_assessment",
                    "ally_locations"
                ]
            
            if memory_duration is None:
                memory_duration = {
                    "player_last_seen": 300.0,
                    "combat_experience": 1800.0,
                    "patrol_routes": -1.0,  # Permanente
                    "threat_assessment": 600.0,
                    "ally_locations": 60.0
                }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "memory_types": memory_types,
                "memory_duration": memory_duration,
                "shared_memory": shared_memory
            }
            
            logger.info(f"Setting up AI memory system with params: {command_data}")
            response = unreal.send_command("setup_ai_memory_system", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI memory system setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up AI memory system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def optimize_ai_performance(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any] = None,
        performance_targets: Dict[str, float] = None
    ) -> Dict[str, Any]:
        """
        Otimiza performance do sistema de IA.
        
        Args:
            layer_name: Nome da camada de IA
            optimization_settings: Configurações de otimização
            performance_targets: Alvos de performance
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if optimization_settings is None:
                optimization_settings = {
                    "update_frequency": 30.0,  # Hz
                    "lod_system_enabled": True,
                    "batch_processing": True,
                    "multithreading": True,
                    "memory_pooling": True
                }
            
            if performance_targets is None:
                performance_targets = {
                    "max_frame_time": 16.67,  # ms (60 FPS)
                    "max_memory_usage": 512.0,  # MB
                    "max_ai_entities": 100,
                    "decision_tree_depth": 10
                }
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings,
                "performance_targets": performance_targets
            }
            
            logger.info(f"Optimizing AI performance with params: {command_data}")
            response = unreal.send_command("optimize_ai_performance", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI performance optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing AI performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_ai_system(
        ctx: Context,
        layer_name: str,
        debug_options: List[str] = None,
        visualization_enabled: bool = True
    ) -> Dict[str, Any]:
        """
        Ativa ferramentas de debug para o sistema de IA.
        
        Args:
            layer_name: Nome da camada de IA
            debug_options: Opções de debug disponíveis
            visualization_enabled: Se visualizações de debug estão ativas
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if debug_options is None:
                debug_options = [
                    "decision_tree_visualization",
                    "behavior_state_display",
                    "communication_lines",
                    "memory_contents",
                    "learning_progress",
                    "performance_metrics"
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "debug_options": debug_options,
                "visualization_enabled": visualization_enabled
            }
            
            logger.info(f"Debugging AI system with params: {command_data}")
            response = unreal.send_command("debug_ai_system", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI system debug response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging AI system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_ai_setup(
        ctx: Context,
        layer_name: str,
        validation_checks: List[str] = None
    ) -> Dict[str, Any]:
        """
        Valida configuração do sistema de IA.
        
        Args:
            layer_name: Nome da camada de IA
            validation_checks: Lista de verificações a realizar
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            if validation_checks is None:
                validation_checks = [
                    "learning_pipeline_integrity",
                    "behavior_tree_validity",
                    "communication_channels",
                    "memory_system_health",
                    "performance_thresholds",
                    "decision_tree_completeness",
                    "spawn_system_configuration"
                ]
            
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "validation_checks": validation_checks
            }
            
            logger.info(f"Validating AI setup with params: {command_data}")
            response = unreal.send_command("validate_ai_setup", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI setup validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating AI setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_ai_system_status(
        ctx: Context,
        layer_name: str,
        include_metrics: bool = True,
        include_health_check: bool = True
    ) -> Dict[str, Any]:
        """
        Obtém status atual do sistema de IA.
        
        Args:
            layer_name: Nome da camada de IA
            include_metrics: Se deve incluir métricas de performance
            include_health_check: Se deve incluir verificação de saúde
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            command_data = {
                "layer_name": layer_name,
                "include_metrics": include_metrics,
                "include_health_check": include_health_check
            }
            
            logger.info(f"Getting AI system status with params: {command_data}")
            response = unreal.send_command("get_ai_system_status", command_data)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"AI system status response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error getting AI system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("AI tools registered successfully")