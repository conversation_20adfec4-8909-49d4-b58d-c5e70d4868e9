<?xml version="1.0" encoding="UTF-8"?>
<FGenericCrashContext>
	<RuntimeProperties>
		<CrashVersion>3</CrashVersion>
		<ExecutionGuid>EF6BA01048CF3D57FB8F22941BEF27EF</ExecutionGuid>
		<CrashGUID>UECC-Windows-40DC97AF43F3E5ADFC7E079BF8881EF6_0000</CrashGUID>
		<IsEnsure>false</IsEnsure>
		<IsStall>false</IsStall>
		<IsAssert>true</IsAssert>
		<CrashType>Assert</CrashType>
		<ErrorMessage>Assertion failed: !m_IsReadOnly [File:D:\build\++UE5\Sync\Engine\Source\Developer\Zen\Private\ZenServerState.cpp] [Line: 342] 

</ErrorMessage>
		<CrashReporterMessage />
		<CrashReporterMessage>Attended</CrashReporterMessage>
		<ProcessId>3716</ProcessId>
		<SecondsSinceStart>0</SecondsSinceStart>
		<IsInternalBuild>false</IsInternalBuild>
		<IsPerforceBuild>false</IsPerforceBuild>
		<IsWithDebugInfo>true</IsWithDebugInfo>
		<IsSourceDistribution>false</IsSourceDistribution>
		<GameName>UE-Auracron</GameName>
		<ExecutableName>UnrealEditor</ExecutableName>
		<BuildConfiguration>Development</BuildConfiguration>
		<GameSessionID />
		<PlatformName>WindowsEditor</PlatformName>
		<PlatformFullName>Win64 [Windows 11 (24H2) [10.0.26100.4946]  64b]</PlatformFullName>
		<PlatformNameIni>Windows</PlatformNameIni>
		<EngineMode>Editor</EngineMode>
		<EngineModeEx>Unset</EngineModeEx>
		<DeploymentName>UE_5.6</DeploymentName>
		<EngineVersion>5.6.1-********+++UE5+Release-5.6</EngineVersion>
		<EngineCompatibleVersion>5.6.1-********+++UE5+Release-5.6</EngineCompatibleVersion>
		<CommandLine>C:/Game/Auracron/Auracron.uproject -AUTH_LOGIN=unused -AUTH_PASSWORD=edf0eb156a894f5a8f4654a56487765e -AUTH_TYPE=exchangecode -epicapp=UE_5.6 -epicenv=Prod -EpicPortal -epicusername=Jukinhaum -epicuserid=1de6ee944444461fafe09fadb52795be -epiclocale=pt-BR -epicsandboxid=ue</CommandLine>
		<LanguageLCID>1046</LanguageLCID>
		<AppDefaultLocale>pt-BR</AppDefaultLocale>
		<BuildVersion>++UE5+Release-5.6-CL-********</BuildVersion>
		<Symbols>**UE5*Release-5.6-CL-********-Win64-Development</Symbols>
		<IsUERelease>true</IsUERelease>
		<IsRequestingExit>false</IsRequestingExit>
		<UserName />
		<BaseDir>C:/Program Files/Epic Games/UE_5.6/Engine/Binaries/Win64/</BaseDir>
		<RootDir>C:/Program Files/Epic Games/UE_5.6/</RootDir>
		<MachineId>8BB1964343E8298F803F869F44351803</MachineId>
		<LoginId>8bb1964343e8298f803f869f44351803</LoginId>
		<EpicAccountId>1de6ee944444461fafe09fadb52795be</EpicAccountId>
		<SourceContext />
		<UserDescription />
		<UserActivityHint />
		<CrashDumpMode>0</CrashDumpMode>
		<GameStateName />
		<Misc.NumberOfCores>10</Misc.NumberOfCores>
		<Misc.NumberOfCoresIncludingHyperthreads>12</Misc.NumberOfCoresIncludingHyperthreads>
		<Misc.Is64bitOperatingSystem>1</Misc.Is64bitOperatingSystem>
		<Misc.CPUVendor>GenuineIntel</Misc.CPUVendor>
		<Misc.CPUBrand>13th Gen Intel(R) Core(TM) i5-1345U</Misc.CPUBrand>
		<Misc.PrimaryGPUBrand>Intel(R) Iris(R) Xe Graphics</Misc.PrimaryGPUBrand>
		<Misc.OSVersionMajor>Windows 11 (24H2) [10.0.26100.4946]</Misc.OSVersionMajor>
		<Misc.OSVersionMinor />
		<Misc.AnticheatProvider />
		<MemoryStats.TotalPhysical>***********</MemoryStats.TotalPhysical>
		<MemoryStats.TotalVirtual>***********</MemoryStats.TotalVirtual>
		<MemoryStats.PageSize>4096</MemoryStats.PageSize>
		<MemoryStats.TotalPhysicalGB>32</MemoryStats.TotalPhysicalGB>
		<MemoryStats.AvailablePhysical>***********</MemoryStats.AvailablePhysical>
		<MemoryStats.AvailableVirtual>24109551616</MemoryStats.AvailableVirtual>
		<MemoryStats.UsedPhysical>939769856</MemoryStats.UsedPhysical>
		<MemoryStats.PeakUsedPhysical>962736128</MemoryStats.PeakUsedPhysical>
		<MemoryStats.UsedVirtual>943149056</MemoryStats.UsedVirtual>
		<MemoryStats.PeakUsedVirtual>965206016</MemoryStats.PeakUsedVirtual>
		<MemoryStats.bIsOOM>0</MemoryStats.bIsOOM>
		<MemoryStats.OOMAllocationSize>0</MemoryStats.OOMAllocationSize>
		<MemoryStats.OOMAllocationAlignment>0</MemoryStats.OOMAllocationAlignment>
		<NumMinidumpFramesToIgnore>6</NumMinidumpFramesToIgnore>
		<CallStack>UnrealEditor_Core
UnrealEditor_Zen
UnrealEditor_Zen
UnrealEditor_Zen
UnrealEditor_Zen
UnrealEditor_Zen
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor_DerivedDataCache
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
UnrealEditor
kernel32
ntdll</CallStack>
		<PCallStack>

UnrealEditor-Core 0x00007ffb8e190000 + 424028 
UnrealEditor-Zen 0x00007ffba3140000 + 984d5 
UnrealEditor-Zen 0x00007ffba3140000 + a80a6 
UnrealEditor-Zen 0x00007ffba3140000 + 8cafa 
UnrealEditor-Zen 0x00007ffba3140000 + a40c7 
UnrealEditor-Zen 0x00007ffba3140000 + 8bc66 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 1770aa 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 184c70 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + eaa13 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + e9e68 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea4c7 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea6df 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea58c 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 9cdbe 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 9d41d 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + c82f4 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + db74c 
UnrealEditor 0x00007ff62d5e0000 + 3f250 
UnrealEditor 0x00007ff62d5e0000 + 2e376 
UnrealEditor 0x00007ff62d5e0000 + 2e6ba 
UnrealEditor 0x00007ff62d5e0000 + 3209e 
UnrealEditor 0x00007ff62d5e0000 + 44e44 
UnrealEditor 0x00007ff62d5e0000 + 480fa 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</PCallStack>
		<PCallStackHash>A6939227204474937635C77578E81C74AA3C3599</PCallStackHash>
		<Threads>
			<Thread>
				<CallStack>KERNELBASE 0x00007ffc365c0000 + c7f7a 
UnrealEditor-Core 0x00007ffb8e190000 + 719db2 
UnrealEditor-Core 0x00007ffb8e190000 + 71d3bb 
UnrealEditor-Core 0x00007ffb8e190000 + 50d902 
UnrealEditor-Core 0x00007ffb8e190000 + 422d27 
UnrealEditor-Core 0x00007ffb8e190000 + 4240ed 
UnrealEditor-Core 0x00007ffb8e190000 + 424028 
UnrealEditor-Zen 0x00007ffba3140000 + 984d5 
UnrealEditor-Zen 0x00007ffba3140000 + a80a6 
UnrealEditor-Zen 0x00007ffba3140000 + 8cafa 
UnrealEditor-Zen 0x00007ffba3140000 + a40c7 
UnrealEditor-Zen 0x00007ffba3140000 + 8bc66 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 1770aa 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 184c70 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + eaa13 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + e9e68 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea4c7 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea6df 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + ea58c 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 9cdbe 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + 9d41d 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + c82f4 
UnrealEditor-DerivedDataCache 0x00007ffba90b0000 + db74c 
UnrealEditor 0x00007ff62d5e0000 + 3f250 
UnrealEditor 0x00007ff62d5e0000 + 2e376 
UnrealEditor 0x00007ff62d5e0000 + 2e6ba 
UnrealEditor 0x00007ff62d5e0000 + 3209e 
UnrealEditor 0x00007ff62d5e0000 + 44e44 
UnrealEditor 0x00007ff62d5e0000 + 480fa 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>true</IsCrashed>
				<Registers />
				<ThreadID>26888</ThreadID>
				<ThreadName>GameThread</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166564 
ntdll 0x00007ffc39120000 + 9259e 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>27228</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166564 
ntdll 0x00007ffc39120000 + 9259e 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>7080</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166564 
ntdll 0x00007ffc39120000 + 9259e 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>30100</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162fd4 
KERNELBASE 0x00007ffc365c0000 + c0ad4 
UnrealEditor-Core 0x00007ffb8e190000 + 718995 
UnrealEditor-Core 0x00007ffb8e190000 + 71a6dd 
UnrealEditor-Core 0x00007ffb8e190000 + 70ca15 
UnrealEditor-Core 0x00007ffb8e190000 + 71c699 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>7720</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-TraceLog 0x00007ffc19940000 + 12fe5 
UnrealEditor-TraceLog 0x00007ffc19940000 + 1086 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>28736</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>14852</ThreadID>
				<ThreadName>BackgroundThreadPool #0</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>32604</ThreadID>
				<ThreadName>BackgroundThreadPool #1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>31384</ThreadID>
				<ThreadName>Foreground Worker #0</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>12360</ThreadID>
				<ThreadName>Foreground Worker #1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>30324</ThreadID>
				<ThreadName>Background Worker #0</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>24472</ThreadID>
				<ThreadName>Background Worker #1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>18868</ThreadID>
				<ThreadName>Background Worker #2</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>21376</ThreadID>
				<ThreadName>Background Worker #3</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>17972</ThreadID>
				<ThreadName>Background Worker #4</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>32068</ThreadID>
				<ThreadName>Background Worker #5</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>34932</ThreadID>
				<ThreadName>Background Worker #6</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + ffd85 
UnrealEditor-Core 0x00007ffb8e190000 + e2ac7 
UnrealEditor-Core 0x00007ffb8e190000 + 113f09 
UnrealEditor-Core 0x00007ffb8e190000 + d5fe5 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>21948</ThreadID>
				<ThreadName>Background Worker #7</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-Core 0x00007ffb8e190000 + 51dca2 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>3748</ThreadID>
				<ThreadName>FAsyncWriter_Auracron</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-Core 0x00007ffb8e190000 + 71f44e 
UnrealEditor-AssetRegistry 0x00007ffbbaf20000 + 93f43 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>18188</ThreadID>
				<ThreadName>FAssetDataDiscovery</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166564 
ntdll 0x00007ffc39120000 + 9259e 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>28132</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
EOSSDK-Win64-Shipping 0x00007ffb67bb0000 + aac54b 
EOSSDK-Win64-Shipping 0x00007ffb67bb0000 + a8e247 
EOSSDK-Win64-Shipping 0x00007ffb67bb0000 + a8e10c 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>25992</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 163404 
KERNELBASE 0x00007ffc365c0000 + 4df43 
UnrealEditor-HTTP 0x00007ffba88b0000 + 25b884 
UnrealEditor-HTTP 0x00007ffba88b0000 + 2592ed 
UnrealEditor-HTTP 0x00007ffba88b0000 + af331 
UnrealEditor-HTTP 0x00007ffba88b0000 + be5f8 
UnrealEditor-HTTP 0x00007ffba88b0000 + ba68f 
UnrealEditor-HTTP 0x00007ffba88b0000 + bb456 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>19688</ThreadID>
				<ThreadName>HttpManagerThread</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-WebSockets 0x00007ffb678a0000 + 8114c 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>21332</ThreadID>
				<ThreadName>LibwebsocketsThread</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-OnlineSubsystem 0x00007ffb6ad70000 + c8707 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>23008</ThreadID>
				<ThreadName>OnlineAsyncTaskThreadNull DefaultInstance(1)</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Media 0x00007ffc0b3f0000 + 7511 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>5176</ThreadID>
				<ThreadName>FMediaTicker</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166504 
ntdll 0x00007ffc39120000 + 3a303 
KERNELBASE 0x00007ffc365c0000 + e49e8 
UnrealEditor-Core 0x00007ffb8e190000 + 43e5dc 
UnrealEditor-Core 0x00007ffb8e190000 + 112c20 
UnrealEditor-Core 0x00007ffb8e190000 + 1123fc 
UnrealEditor-Core 0x00007ffb8e190000 + 524da0 
UnrealEditor-Core 0x00007ffb8e190000 + 2f1a33 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>28424</ThreadID>
				<ThreadName>OutputDeviceRedirector</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>12880</ThreadID>
				<ThreadName>IOThreadPool #0</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>29452</ThreadID>
				<ThreadName>IOThreadPool #1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>19292</ThreadID>
				<ThreadName>IOThreadPool #2</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>8576</ThreadID>
				<ThreadName>IOThreadPool #3</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-Core 0x00007ffb8e190000 + 71f44e 
UnrealEditor-ApplicationCore 0x00007ffbc0bb0000 + 66b91 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>30568</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166504 
ntdll 0x00007ffc39120000 + a9e5e 
KERNELBASE 0x00007ffc365c0000 + 4dc18 
igd10um64xe 0x00007ffc2acc0000 + e26b9 
igd10um64xe 0x00007ffc2acc0000 + a914 
igd10um64xe 0x00007ffc2acc0000 + a878 
igd10um64xe 0x00007ffc2acc0000 + a7e0 
igd10um64xe 0x00007ffc2acc0000 + a6f9 
igd10um64xe 0x00007ffc2acc0000 + cf576 
igd10um64xe 0x00007ffc2acc0000 + 117edf 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>14712</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 166504 
ntdll 0x00007ffc39120000 + a9e5e 
KERNELBASE 0x00007ffc365c0000 + 4dc18 
igd10um64xe 0x00007ffc2acc0000 + e26b9 
igd10um64xe 0x00007ffc2acc0000 + a914 
igd10um64xe 0x00007ffc2acc0000 + a878 
igd10um64xe 0x00007ffc2acc0000 + a7e0 
igd10um64xe 0x00007ffc2acc0000 + ab50 
igd10um64xe 0x00007ffc2acc0000 + aa94 
igd10um64xe 0x00007ffc2acc0000 + cf576 
igd10um64xe 0x00007ffc2acc0000 + 117edf 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>19196</ThreadID>
				<ThreadName>Unknown</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162f34 
ntdll 0x00007ffc39120000 + ddb04 
KERNELBASE 0x00007ffc365c0000 + 76671 
UnrealEditor-Core 0x00007ffb8e190000 + 71f44e 
UnrealEditor-Core 0x00007ffb8e190000 + 51dfcd 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>26040</ThreadID>
				<ThreadName>FMonitoredProcess 0</ThreadName>
			</Thread>
			<Thread>
				<CallStack />
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>30424</ThreadID>
				<ThreadName>FIOSDeviceHelper.QueryTask_1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>19148</ThreadID>
				<ThreadName>DDC IO ThreadPool #0</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>19792</ThreadID>
				<ThreadName>DDC IO ThreadPool #1</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>4936</ThreadID>
				<ThreadName>DDC IO ThreadPool #2</ThreadName>
			</Thread>
			<Thread>
				<CallStack>ntdll 0x00007ffc39120000 + 162934 
KERNELBASE 0x00007ffc365c0000 + 4cbbf 
UnrealEditor-Core 0x00007ffb8e190000 + 72256d 
UnrealEditor-Core 0x00007ffb8e190000 + 2f17db 
UnrealEditor-Core 0x00007ffb8e190000 + 7937cd 
UnrealEditor-Core 0x00007ffb8e190000 + 78c91f 
KERNEL32 0x00007ffc38540000 + 2e8d7 
ntdll 0x00007ffc39120000 + 3c34c 
</CallStack>
				<IsCrashed>false</IsCrashed>
				<Registers />
				<ThreadID>21912</ThreadID>
				<ThreadName>DDC IO ThreadPool #3</ThreadName>
			</Thread>
		</Threads>
		<TimeOfCrash>638914739642850000</TimeOfCrash>
		<bAllowToBeContacted>1</bAllowToBeContacted>
		<CPUBrand>13th Gen Intel(R) Core(TM) i5-1345U</CPUBrand>
		<CrashReportClientVersion>1.0</CrashReportClientVersion>
		<Modules>C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Python3\Win64\python3.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor.exe
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\metalirconverter.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MetalShaderFormat.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\ShaderConductor\Win64\ShaderConductor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-VulkanShaderFormat.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ShaderFormatOpenGL.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-D3D11RHI.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\ShaderConductor\Win64\dxcompiler.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\ShaderConductor\Win64\dxil.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ShaderFormatD3D.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ShaderFormatVectorVM.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ShaderCompilerCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-FileUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ShaderPreprocessor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioFormatRad.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioFormatBink.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioFormatADPCM.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioFormatOgg.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioFormatOpus.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WindowsTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WindowsTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WindowsTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-TVOSTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-TVOSTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-TVOSTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MacTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MacTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CookedEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MacTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Linux\UnrealEditor-LinuxTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Android\UnrealEditor-AndroidTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Android\UnrealEditor-AndroidTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Android\UnrealEditor-AndroidTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\oo2tex_win64_2.9.5.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\oo2tex_win64_2.9.13.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\astcenc_thunk_win64_5.0.1.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Developer\TextureFormatOodle\Binaries\Win64\UnrealEditor-TextureFormatOodle.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormatUncompressed.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormatIntelISPCTexComp.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormatETC2.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormatDXT.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormatASTC.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureBuild.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureFormat.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TurnkeySupport.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-LauncherServices.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Linux\UnrealEditor-LinuxTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\Linux\UnrealEditor-LinuxTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-IOSTargetPlatformControls.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-IOSTargetPlatformSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\IOS\UnrealEditor-IOSTargetPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Settings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WindowsPlatformFeatures.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GameplayMediaEncoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AVEncoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\D3D12\x64\D3D12Core.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-D3D12RHI.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RHICore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\Compositing\CompositeCore\Binaries\Win64\UnrealEditor-CompositeCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\VirtualProduction\CameraCalibrationCore\Binaries\Win64\UnrealEditor-CameraCalibrationCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ProceduralMeshComponent\Binaries\Win64\UnrealEditor-ProceduralMeshComponent.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\WindowsDeviceProfileSelector\Binaries\Win64\UnrealEditor-WindowsDeviceProfileSelector.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\HairStrands\Binaries\Win64\UnrealEditor-HairStrandsCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ChunkDownloader\Binaries\Win64\UnrealEditor-ChunkDownloader.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemNull\Binaries\Win64\UnrealEditor-OnlineSubsystemNull.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Binaries\Win64\UnrealEditor-OnlineSubsystemUtils.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-XMPP.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WebSockets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\EOSSDK-Win64-Shipping.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Portal\LauncherChunkInstaller\Binaries\Win64\UnrealEditor-LauncherChunkInstaller.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystemUtils\Binaries\Win64\UnrealEditor-OnlineBlueprintSupport.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Voice.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineSubsystem\Binaries\Win64\UnrealEditor-OnlineSubsystem.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineServices\Binaries\Win64\UnrealEditor-OnlineServicesCommonEngineUtils.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineServices\Binaries\Win64\UnrealEditor-OnlineServicesCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineServices\Binaries\Win64\UnrealEditor-OnlineServicesInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\EOSShared\Binaries\Win64\UnrealEditor-EOSShared.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Media\WmfMedia\Binaries\Win64\UnrealEditor-WmfMedia.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\NFORDenoise\Binaries\Win64\UnrealEditor-NFORDenoise.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorTelemetry\Binaries\Win64\UnrealEditor-EditorTelemetry.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorPerformance\Binaries\Win64\UnrealEditor-StallLogSubsystem.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\EditorPerformance\Binaries\Win64\UnrealEditor-EditorPerformance.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Enterprise\GLTFExporter\Binaries\Win64\UnrealEditor-GLTFExporter.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Interchange\Runtime\Binaries\Win64\UnrealEditor-InterchangeFactoryNodes.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Interchange\Runtime\Binaries\Win64\UnrealEditor-InterchangeNodes.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\GeometryCache\Binaries\Win64\UnrealEditor-GeometryCache.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Binaries\Win64\UnrealEditor-Niagara.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-VectorVM.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Binaries\Win64\UnrealEditor-NiagaraVertexFactories.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Binaries\Win64\UnrealEditor-NiagaraShader.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Enterprise\DatasmithContent\Binaries\Win64\UnrealEditor-DatasmithContent.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Enterprise\VariantManagerContent\Binaries\Win64\UnrealEditor-VariantManagerContent.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ComputeFramework\Binaries\Win64\UnrealEditor-ComputeFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Animation\ACLPlugin\Binaries\Win64\UnrealEditor-ACLPlugin.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\ChaosCloth\Binaries\Win64\UnrealEditor-ChaosCloth.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\ChaosCaching\Binaries\Win64\UnrealEditor-ChaosCaching.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Python3\Win64\python311.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\PlatformCrypto\Binaries\Win64\UnrealEditor-PlatformCryptoContext.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Developer\PlasticSourceControl\Binaries\Win64\UnrealEditor-PlasticSourceControl.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Developer\PerforceSourceControl\Binaries\Win64\UnrealEditor-PerforceSourceControl.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealBuildAccelerator\x64\UbaHost.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UbaCoordinatorHorde.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Vorbis\Win64\VS2015\libvorbis_64.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\DbgHelp\dbghelp.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimationModifiers.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\libsndfile\Win64\libsndfile-1.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RadAudioDecoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Online\OnlineBase\Binaries\Win64\UnrealEditor-OnlineBase.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Developer\RenderDocPlugin\Binaries\Win64\UnrealEditor-RenderDocPlugin.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MessageLog.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Virtualization.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StorageServerClient.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\NNE\NNEDenoiser\Binaries\Win64\UnrealEditor-NNEDenoiserShaders.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Developer\PixWinPlugin\Binaries\Win64\UnrealEditor-PixWinPlugin.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DataflowSimulation.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GameplayDebugger.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GameplayTasks.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-LiveLinkInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SequenceRecorder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ISMPool.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-FieldSystemEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DataflowEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DataflowCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ChaosSolverEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshConversion.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MovieSceneCapture.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SequencerCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StorageServerWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AIModule.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MovieSceneTools.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Chaos.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AdvancedWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ComponentVisualizers.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ContentBrowser.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-OpenColorIOWrapper.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InternationalizationSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SlateReflector.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-OpusAudioDecoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioSettingsEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Blutility.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Sequencer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DetailCustomizations.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Media\WmfMedia\Binaries\Win64\UnrealEditor-WmfMediaFactory.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UELibSampleRate.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GeometryCollectionEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SlateRHIRenderer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Renderer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Media\ImgMedia\Binaries\Win64\UnrealEditor-ExrReaderGpu.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Navmesh.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Constraints.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DerivedDataWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimationBlueprintLibrary.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimationCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Interchange\Assets\Binaries\Win64\UnrealEditor-InterchangeAssets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ActionableMessage.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SourceControlWindows.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ContentBrowserData.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ClassViewer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PropertyPath.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorStyle.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UMGEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorConfig.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UnrealEd.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ConfigEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimGraphRuntime.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-KismetCompiler.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-KismetWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SceneOutliner.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MediaAssets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-IrisCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SkeletalMeshDescription.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioMixer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PhysicsCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AssetTools.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UMG.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Engine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Core.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-NetworkFile.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ToolWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-BlueprintGraph.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MovieSceneTracks.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MovieScene.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\RuntimeTelemetry\Binaries\Win64\UnrealEditor-RuntimeTelemetry.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GameplayTags.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Landscape.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Kismet.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AVIWriter.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InterchangeCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StudioTelemetry.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GeometryCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PropertyEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MaterialEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Interchange\Runtime\Binaries\Win64\UnrealEditor-InterchangeCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DesktopWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InteractiveToolsFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-LevelEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-HeadMountedDisplay.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PhysicsUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GraphEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ImageWrapper.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AppFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Voronoi.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-NavigationSystem.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimGraph.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UnsavedAssetsTracker.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-NetCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ChaosVDRuntime.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TraceServices.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\libfbxsdk.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DerivedDataEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Zen.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FX\Niagara\Binaries\Win64\UnrealEditor-NiagaraCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Animation\DeformerGraph\Binaries\Win64\UnrealEditor-OptimusSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InterchangeEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-IoStoreUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ZenEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WidgetRegistration.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\XGEController\Binaries\Win64\UnrealEditor-XGEController.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TypedElementFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DevHttp.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SlateCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\UbaController\Binaries\Win64\UnrealEditor-UbaController.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-GameProjectGeneration.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Slate.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-BinkAudioDecoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StatusBar.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-HTTP.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SubobjectDataInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StaticMeshDescription.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DerivedDataCache.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Localization.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AdpcmAudioDecoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SubobjectEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-FoliageEdit.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Ogg\Win64\VS2015\libogg_64.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshDescription.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RenderCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-VorbisAudioDecoder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CoreUObject.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshBuilder.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ViewportInteraction.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MaterialShaderQualitySettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshUtilitiesCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SourceControl.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-WidgetCarousel.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CQTest.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CurveEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SharedSettingsWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorInteractiveToolsFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-VREditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\PythonScriptPlugin\Binaries\Win64\UnrealEditor-PythonScriptPluginPreload.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ToolMenus.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StreamingFile.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Documentation.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SSL.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\PlatformCrypto\Binaries\Win64\UnrealEditor-PlatformCrypto.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\FastBuildController\Binaries\Win64\UnrealEditor-FastBuildController.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MaterialBaking.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Foliage.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SerializedRecorderInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AdvancedPreviewScene.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-BlueprintEditorLibrary.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PakFileUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshBuilderCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AddContentDialog.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ChaosCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ReliabilityHandlerComponent.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StatsViewer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Horde.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AssetRegistry.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioLinkEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ClothingSystemRuntimeCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DataLayerEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SequencerWidgets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PakFile.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-HardwareTargeting.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SoundFieldRendering.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SkeletalMeshUtilitiesCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TraceAnalysis.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MeshUtilitiesEngine.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SceneDepthPickerMode.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Networking.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SignalProcessing.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TimeManagement.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnalyticsET.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PIEPreviewDeviceSpecification.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioExtensions.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PIEPreviewDeviceProfileSelector.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-LevelSequence.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Json.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MaterialUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ActorPickerMode.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RSA.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ApplicationCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RHI.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CommonMenuExtensions.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AnimationEditMode.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TelemetryUtils.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TypedElementRuntime.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ClothingSystemRuntimeInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UncontrolledChangelists.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-NNE.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureCompressor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureBuildUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Icmp.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Analytics.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioLinkCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-StateStream.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PacketHandler.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CinematicCamera.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Projects.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ImageCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DesktopPlatform.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorFramework.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EngineMessages.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-NetworkReplayStreaming.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DeveloperToolSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MediaUtils.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioPlatformConfiguration.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CoreOnline.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ClothingSystemEditorInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DirectoryWatcher.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UniversalObjectLocator.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AssetDefinition.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-HierarchicalLODUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InputCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-RawMesh.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-JsonUtilities.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-XmlParser.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-FieldNotification.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-DeveloperSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TextureUtilitiesCommon.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CookOnTheFly.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AudioMixerCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorAnalyticsSession.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EditorSubsystem.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-UnrealEdMessages.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SandboxFile.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-InstallBundleManager.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ScriptDisassembler.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Sockets.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-BSPUtils.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-JsonObjectGraph.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-SwarmInterface.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EventLoop.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-IESFile.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-EngineSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AssetTagsEditor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-ImageWriteQueue.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-PreLoadScreen.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Runtime\ExampleDeviceProfileSelector\Binaries\Win64\UnrealEditor-ExampleDeviceProfileSelector.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Nanosvg.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CookMetadata.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\AI\AISupport\Binaries\Win64\UnrealEditor-AISupportModule.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Vorbis\Win64\VS2015\libvorbisfile_64.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-IoStoreOnDemandCore.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Plugins\Experimental\PlatformCrypto\Binaries\Win64\UnrealEditor-PlatformCryptoTypes.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Media.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MoviePlayer.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\ThirdParty\Windows\WinPixEventRuntime\x64\WinPixEventRuntime.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-CorePreciseFP.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-Cbor.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-AutoRTFM.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\tbbmalloc.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-BuildSettings.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-TraceLog.dll
C:\Program Files\Epic Games\UE_5.6\Engine\Binaries\Win64\UnrealEditor-MoviePlayerProxy.dll</Modules>
	</RuntimeProperties>
	<PlatformProperties>
		<PlatformIsRunningWindows>1</PlatformIsRunningWindows>
		<PlatformIsRunningWine>false</PlatformIsRunningWine>
		<PlatformSupportsWindows11>true</PlatformSupportsWindows11>
		<IsRunningOnBattery>true</IsRunningOnBattery>
		<IsUsingBatterySaver>false</IsUsingBatterySaver>
		<PlatformCallbackResult>0</PlatformCallbackResult>
		<CrashTrigger>0</CrashTrigger>
	</PlatformProperties>
	<EngineData>
		<MatchingDPStatus>WindowsEditorNo errors</MatchingDPStatus>
		<RHI.IntegratedGPU>true</RHI.IntegratedGPU>
		<RHI.DriverDenylisted>false</RHI.DriverDenylisted>
		<RHI.D3DDebug>false</RHI.D3DDebug>
		<RHI.Aftermath>false</RHI.Aftermath>
		<RHI.IsGPUOverclocked>false</RHI.IsGPUOverclocked>
		<RHI.RHIName>D3D11</RHI.RHIName>
		<RHI.AdapterName>Intel(R) Iris(R) Xe Graphics</RHI.AdapterName>
		<RHI.UserDriverVersion>32.0.101.6556</RHI.UserDriverVersion>
		<RHI.InternalDriverVersion>32.0.101.6556</RHI.InternalDriverVersion>
		<RHI.DriverDate>1-23-2025</RHI.DriverDate>
		<RHI.FeatureLevel>SM5</RHI.FeatureLevel>
		<RHI.GPUVendor>Intel</RHI.GPUVendor>
		<RHI.DeviceId>A7A1</RHI.DeviceId>
	</EngineData>
	<GameData>
</GameData>
</FGenericCrashContext>
