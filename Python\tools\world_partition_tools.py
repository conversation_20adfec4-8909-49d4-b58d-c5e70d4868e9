# -*- coding: utf-8 -*-
"""
World Partition Tools for Auracron
Ferramentas para gerenciar o sistema de World Partition Multicamada
"""

import unreal
from unreal_mcp_server import unreal_connection
import logging

logger = logging.getLogger(__name__)

def create_world_partition_level(level_name, layer_type="Firmamento", grid_size=25600, cell_size=1600):
    """
    Cria um novo level com World Partition configurado para multicamadas
    
    Args:
        level_name (str): Nome do level a ser criado
        layer_type (str): T<PERSON><PERSON> da camada (Firmamento, Planicie, Abismo)
        grid_size (int): Tamanho total da grid em unidades Unreal
        cell_size (int): Tamanho de cada célula de streaming
    
    Returns:
        dict: Resultado da operação
    """
    try:
        command_data = {
            "command": "create_world_partition_level",
            "level_name": level_name,
            "layer_type": layer_type,
            "grid_size": grid_size,
            "cell_size": cell_size
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"World Partition level criado: {level_name}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao criar World Partition level: {e}")
        return {"success": False, "error": str(e)}

def configure_streaming_cell(level_name, cell_x, cell_y, layer_type, streaming_distance=5000, priority=1):
    """
    Configura uma célula de streaming específica
    
    Args:
        level_name (str): Nome do level
        cell_x (int): Coordenada X da célula
        cell_y (int): Coordenada Y da célula
        layer_type (str): Tipo da camada
        streaming_distance (float): Distância de streaming
        priority (int): Prioridade de carregamento (1-10)
    
    Returns:
        dict: Resultado da operação
    """
    try:
        command_data = {
            "command": "configure_streaming_cell",
            "level_name": level_name,
            "cell_x": cell_x,
            "cell_y": cell_y,
            "layer_type": layer_type,
            "streaming_distance": streaming_distance,
            "priority": priority
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"Célula de streaming configurada: {cell_x}, {cell_y}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao configurar célula de streaming: {e}")
        return {"success": False, "error": str(e)}

def create_layer_hierarchy(parent_layer, child_layer, transition_type="Portal"):
    """
    Cria hierarquia entre camadas para transições
    
    Args:
        parent_layer (str): Camada pai
        child_layer (str): Camada filha
        transition_type (str): Tipo de transição (Portal, Seamless, Teleport)
    
    Returns:
        dict: Resultado da operação
    """
    try:
        command_data = {
            "command": "create_layer_hierarchy",
            "parent_layer": parent_layer,
            "child_layer": child_layer,
            "transition_type": transition_type
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"Hierarquia de camadas criada: {parent_layer} -> {child_layer}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao criar hierarquia de camadas: {e}")
        return {"success": False, "error": str(e)}

def set_spatial_division_rules(layer_type, division_algorithm="Quadtree", max_depth=8, min_objects_per_cell=10):
    """
    Define regras de divisão espacial para uma camada
    
    Args:
        layer_type (str): Tipo da camada
        division_algorithm (str): Algoritmo de divisão (Quadtree, Octree, Grid)
        max_depth (int): Profundidade máxima da árvore
        min_objects_per_cell (int): Mínimo de objetos por célula
    
    Returns:
        dict: Resultado da operação
    """
    try:
        command_data = {
            "command": "set_spatial_division_rules",
            "layer_type": layer_type,
            "division_algorithm": division_algorithm,
            "max_depth": max_depth,
            "min_objects_per_cell": min_objects_per_cell
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"Regras de divisão espacial definidas para: {layer_type}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao definir regras de divisão espacial: {e}")
        return {"success": False, "error": str(e)}

def configure_layer_streaming_manager(layer_type, streaming_policy="Distance", memory_budget_mb=512):
    """
    Configura o gerenciador de streaming para uma camada
    
    Args:
        layer_type (str): Tipo da camada
        streaming_policy (str): Política de streaming (Distance, Priority, Memory)
        memory_budget_mb (int): Orçamento de memória em MB
    
    Returns:
        dict: Resultado da operação
    """
    try:
        # Validar streaming_policy
        valid_policies = ["Distance", "Priority", "Memory"]
        if streaming_policy not in valid_policies:
            logger.error(f"Invalid streaming_policy: {streaming_policy}. Valid options: {valid_policies}")
            return {"success": False, "error": f"Invalid streaming_policy: {streaming_policy}"}
        
        command_data = {
            "command": "configure_layer_streaming_manager",
            "layer_type": layer_type,
            "streaming_policy": streaming_policy,
            "memory_budget_mb": memory_budget_mb
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"Streaming manager configurado para: {layer_type}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao configurar streaming manager: {e}")
        return {"success": False, "error": str(e)}

def get_world_partition_status(level_name):
    """
    Obtém o status atual do World Partition de um level
    
    Args:
        level_name (str): Nome do level
    
    Returns:
        dict: Status do World Partition
    """
    try:
        command_data = {
            "command": "get_world_partition_status",
            "level_name": level_name
        }
        
        response = unreal.send_command(command_data)
        return response
        
    except Exception as e:
        logger.error(f"Erro ao obter status do World Partition: {e}")
        return {"success": False, "error": str(e)}

def optimize_streaming_cells(level_name, optimization_type="Performance"):
    """
    Otimiza as células de streaming de um level
    
    Args:
        level_name (str): Nome do level
        optimization_type (str): Tipo de otimização (Performance, Memory, Quality)
    
    Returns:
        dict: Resultado da otimização
    """
    try:
        command_data = {
            "command": "optimize_streaming_cells",
            "level_name": level_name,
            "optimization_type": optimization_type
        }
        
        response = unreal.send_command(command_data)
        logger.info(f"Células de streaming otimizadas para: {level_name}")
        return response
        
    except Exception as e:
        logger.error(f"Erro ao otimizar células de streaming: {e}")
        return {"success": False, "error": str(e)}