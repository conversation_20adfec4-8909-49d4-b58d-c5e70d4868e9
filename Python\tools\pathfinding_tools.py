import unreal
import json
from typing import Dict, List, Tuple, Optional, Any

def create_navigation_mesh_layer(layer_name: str, layer_height: float, cell_size: float = 64.0, 
                                cell_height: float = 32.0, agent_radius: float = 34.0, 
                                agent_height: float = 144.0, max_slope: float = 44.0) -> Dict[str, Any]:
    """
    Cria uma camada de navigation mesh para pathfinding 3D.
    
    Args:
        layer_name: Nome da camada de navegação
        layer_height: Altura da camada no mundo 3D
        cell_size: Taman<PERSON> das células do navmesh
        cell_height: Altura das células
        agent_radius: Raio do agente para navegação
        agent_height: Altura do agente
        max_slope: Inclinação máxima permitida
    
    Returns:
        Dicionário com informações da camada criada
    """
    command_data = {
        "command": "create_navigation_mesh_layer",
        "layer_name": layer_name,
        "layer_height": layer_height,
        "cell_size": cell_size,
        "cell_height": cell_height,
        "agent_radius": agent_radius,
        "agent_height": agent_height,
        "max_slope": max_slope
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def configure_astar_algorithm(layer_name: str, heuristic_type: str = "euclidean", 
                             weight_factor: float = 1.0, diagonal_cost: float = 1.414,
                             tie_breaker: float = 0.001, max_search_nodes: int = 10000) -> Dict[str, Any]:
    """
    Configura o algoritmo A* para uma camada específica.
    
    Args:
        layer_name: Nome da camada de navegação
        heuristic_type: Tipo de heurística (euclidean, manhattan, diagonal)
        weight_factor: Fator de peso para a heurística
        diagonal_cost: Custo para movimento diagonal
        tie_breaker: Fator para quebrar empates
        max_search_nodes: Número máximo de nós a pesquisar
    
    Returns:
        Dicionário com configuração do algoritmo
    """
    command_data = {
        "command": "configure_astar_algorithm",
        "layer_name": layer_name,
        "heuristic_type": heuristic_type,
        "weight_factor": weight_factor,
        "diagonal_cost": diagonal_cost,
        "tie_breaker": tie_breaker,
        "max_search_nodes": max_search_nodes
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def set_movement_costs(layer_name: str, terrain_costs: Dict[str, float], 
                      layer_transition_costs: Dict[str, float]) -> Dict[str, Any]:
    """
    Define custos de movimento para diferentes tipos de terreno e transições entre camadas.
    
    Args:
        layer_name: Nome da camada de navegação
        terrain_costs: Dicionário com custos por tipo de terreno
        layer_transition_costs: Custos para transição entre camadas
    
    Returns:
        Dicionário com configuração dos custos
    """
    command_data = {
        "command": "set_movement_costs",
        "layer_name": layer_name,
        "terrain_costs": terrain_costs,
        "layer_transition_costs": layer_transition_costs
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def create_layer_connections(source_layer: str, target_layer: str, 
                           connection_points: List[Tuple[float, float, float]],
                           connection_type: str = "portal", bidirectional: bool = True) -> Dict[str, Any]:
    """
    Cria conexões entre camadas de navegação para pathfinding 3D.
    
    Args:
        source_layer: Camada de origem
        target_layer: Camada de destino
        connection_points: Lista de pontos de conexão (x, y, z)
        connection_type: Tipo de conexão (portal, ladder, teleport, jump)
        bidirectional: Se a conexão é bidirecional
    
    Returns:
        Dicionário com informações das conexões criadas
    """
    command_data = {
        "command": "create_layer_connections",
        "source_layer": source_layer,
        "target_layer": target_layer,
        "connection_points": connection_points,
        "connection_type": connection_type,
        "bidirectional": bidirectional
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def configure_pathfinding_constraints(layer_name: str, max_path_length: float = 10000.0,
                                    max_search_time: float = 0.1, allow_partial_paths: bool = True,
                                    smooth_paths: bool = True, path_optimization: str = "string_pulling") -> Dict[str, Any]:
    """
    Configura restrições e otimizações para o pathfinding.
    
    Args:
        layer_name: Nome da camada de navegação
        max_path_length: Comprimento máximo do caminho
        max_search_time: Tempo máximo de busca em segundos
        allow_partial_paths: Permitir caminhos parciais
        smooth_paths: Suavizar caminhos
        path_optimization: Tipo de otimização (string_pulling, funnel, none)
    
    Returns:
        Dicionário com configuração das restrições
    """
    command_data = {
        "command": "configure_pathfinding_constraints",
        "layer_name": layer_name,
        "max_path_length": max_path_length,
        "max_search_time": max_search_time,
        "allow_partial_paths": allow_partial_paths,
        "smooth_paths": smooth_paths,
        "path_optimization": path_optimization
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def create_dynamic_obstacles(layer_name: str, obstacle_data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Cria obstáculos dinâmicos que afetam o pathfinding.
    
    Args:
        layer_name: Nome da camada de navegação
        obstacle_data: Lista de dados dos obstáculos (posição, tamanho, tipo)
    
    Returns:
        Dicionário com informações dos obstáculos criados
    """
    command_data = {
        "command": "create_dynamic_obstacles",
        "layer_name": layer_name,
        "obstacle_data": obstacle_data
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def find_path_multilayer(start_position: Tuple[float, float, float], 
                        end_position: Tuple[float, float, float],
                        agent_properties: Dict[str, Any],
                        allowed_layers: List[str] = None) -> Dict[str, Any]:
    """
    Encontra um caminho através de múltiplas camadas usando A*.
    
    Args:
        start_position: Posição inicial (x, y, z)
        end_position: Posição final (x, y, z)
        agent_properties: Propriedades do agente (raio, altura, velocidade)
        allowed_layers: Lista de camadas permitidas para navegação
    
    Returns:
        Dicionário com o caminho encontrado e informações de navegação
    """
    command_data = {
        "command": "find_path_multilayer",
        "start_position": start_position,
        "end_position": end_position,
        "agent_properties": agent_properties,
        "allowed_layers": allowed_layers or []
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def optimize_navigation_performance(layer_name: str, optimization_settings: Dict[str, Any]) -> Dict[str, Any]:
    """
    Otimiza o desempenho do sistema de navegação.
    
    Args:
        layer_name: Nome da camada de navegação
        optimization_settings: Configurações de otimização
    
    Returns:
        Dicionário com resultados da otimização
    """
    command_data = {
        "command": "optimize_navigation_performance",
        "layer_name": layer_name,
        "optimization_settings": optimization_settings
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def get_pathfinding_system_status(include_performance_metrics: bool = True) -> Dict[str, Any]:
    """
    Obtém o status do sistema de pathfinding multicamada.
    
    Args:
        include_performance_metrics: Incluir métricas de performance
    
    Returns:
        Dicionário com status do sistema
    """
    command_data = {
        "command": "get_pathfinding_system_status",
        "include_performance_metrics": include_performance_metrics
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def configure_hierarchical_pathfinding(layer_name: str, cluster_size: int = 10,
                                     max_hierarchy_levels: int = 3,
                                     precompute_clusters: bool = True) -> Dict[str, Any]:
    """
    Configura pathfinding hierárquico para melhor performance em mapas grandes.
    
    Args:
        layer_name: Nome da camada de navegação
        cluster_size: Tamanho dos clusters para hierarquia
        max_hierarchy_levels: Número máximo de níveis hierárquicos
        precompute_clusters: Pré-computar clusters
    
    Returns:
        Dicionário com configuração hierárquica
    """
    command_data = {
        "command": "configure_hierarchical_pathfinding",
        "layer_name": layer_name,
        "cluster_size": cluster_size,
        "max_hierarchy_levels": max_hierarchy_levels,
        "precompute_clusters": precompute_clusters
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def setup_crowd_navigation(layer_name: str, crowd_settings: Dict[str, Any]) -> Dict[str, Any]:
    """
    Configura navegação para multidões com evitamento de colisões.
    
    Args:
        layer_name: Nome da camada de navegação
        crowd_settings: Configurações para navegação de multidões
    
    Returns:
        Dicionário com configuração de multidões
    """
    command_data = {
        "command": "setup_crowd_navigation",
        "layer_name": layer_name,
        "crowd_settings": crowd_settings
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

# Funções auxiliares para debugging e visualização
def debug_navigation_layer(layer_name: str, show_navmesh: bool = True, 
                          show_connections: bool = True, show_obstacles: bool = True) -> Dict[str, Any]:
    """
    Ativa visualização de debug para uma camada de navegação.
    
    Args:
        layer_name: Nome da camada de navegação
        show_navmesh: Mostrar o navmesh
        show_connections: Mostrar conexões entre camadas
        show_obstacles: Mostrar obstáculos
    
    Returns:
        Dicionário com configuração de debug
    """
    command_data = {
        "command": "debug_navigation_layer",
        "layer_name": layer_name,
        "show_navmesh": show_navmesh,
        "show_connections": show_connections,
        "show_obstacles": show_obstacles
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}

def validate_navigation_setup(layer_names: List[str] = None) -> Dict[str, Any]:
    """
    Valida a configuração do sistema de navegação multicamada.
    
    Args:
        layer_names: Lista de camadas para validar (None para todas)
    
    Returns:
        Dicionário com resultados da validação
    """
    command_data = {
        "command": "validate_navigation_setup",
        "layer_names": layer_names or []
    }
    
    result = unreal.send_command(json.dumps(command_data))
    return json.loads(result) if result else {"success": False, "error": "No response"}