// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/Actor.h"
#include "Components/ActorComponent.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInterface.h"
#include "Engine/Texture2D.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Engine/GameEngine.h"
#include "Kismet/GameplayStatics.h"
#include "Engine/LevelStreaming.h"
#include "Engine/WorldComposition.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Streaming/LevelStreamingDelegates.h"
#include "Engine/AssetManager.h"
#include "Engine/StreamableManager.h"
#include "HAL/ThreadSafeBool.h"
#include "Async/AsyncWork.h"
#include "Containers/Queue.h"
#include "Engine/NetDriver.h"
#include "Components/BrushComponent.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "Components/SceneComponent.h"
#include "Engine/CollisionProfile.h"
#include "PhysicsEngine/BodySetup.h"
#include "Components/PrimitiveComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Particles/ParticleSystem.h"
#include "Sound/SoundBase.h"
#include "UObject/SoftObjectPath.h"
#include "Engine/AssetUserData.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "Subsystems/WorldSubsystem.h"
#include "Engine/LevelScriptActor.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Engine/TriggerVolume.h"
#include "GameFramework/Volume.h"
#include "Engine/PostProcessVolume.h"
#include "Components/PostProcessComponent.h"
#include "Engine/ReplicationDriver.h"
#include "Net/UnrealNetwork.h"
#include "GameFramework/GameNetworkManager.h"
#include "Engine/NetConnection.h"
#include "GameFramework/OnlineReplStructs.h"
#include "Engine/DemoNetDriver.h"
#include "Misc/NetworkGuid.h"
#include "GameFramework/Info.h"
#include "GameFramework/GameStateBase.h"
#include "GameFramework/PlayerState.h"
#include "Components/WidgetComponent.h"
#include "Blueprint/UserWidget.h"
#include "Components/TextBlock.h"
#include "Components/Button.h"
#include "Components/Image.h"
#include "Components/ProgressBar.h"
#include "Components/Slider.h"
#include "Animation/AnimationAsset.h"
#include "Animation/AnimSequence.h"
#include "Animation/AnimBlueprint.h"
#include "Animation/AnimInstance.h"
#include "Components/SkeletalMeshComponent.h"
#include "Engine/SkeletalMesh.h"
#include "Materials/MaterialParameterCollection.h"
#include "Materials/MaterialParameterCollectionInstance.h"
#include "Engine/DirectionalLight.h"
#include "Engine/PointLight.h"
#include "Engine/SpotLight.h"
#include "Engine/SkyLight.h"
#include "Components/LightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "Engine/TextureCube.h"
#include "Engine/ReflectionCapture.h"
#include "Components/ReflectionCaptureComponent.h"
#include "Engine/Scene.h"
#include "EngineUtils.h"
#include "Engine/Selection.h"
#include "Editor/EditorEngine.h"
#include "UnrealEdGlobals.h"
#include "Editor/UnrealEdEngine.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Developer/AssetTools/Public/AssetToolsModule.h"
#include "Editor/ContentBrowser/Public/ContentBrowserModule.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Misc/MessageDialog.h"
#include "HAL/PlatformApplicationMisc.h"
#include "GenericPlatform/GenericPlatformMisc.h"
#include "Misc/App.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "HAL/PlatformProcess.h"
#include "Misc/Paths.h"
#include "HAL/FileManager.h"
#include "Misc/ConfigCacheIni.h"
#include "GameFramework/SaveGame.h"
// Kismet/GameplayStatics.h already included above
#include "Engine/LocalPlayer.h"
#include "GameFramework/HUD.h"
#include "Engine/Canvas.h"
#include "Engine/Font.h"
#include "Slate/SceneViewport.h"
#include "Framework/Application/SlateApplication.h"
#include "Input/Events.h"
#include "GenericPlatform/ICursor.h"
#include "Widgets/SViewport.h"
#include "Widgets/SWindow.h"
#include "Framework/Application/SlateUser.h"
#include "Input/Reply.h"
#include "Layout/Geometry.h"
#include "Math/Vector2D.h"
#include "Math/IntPoint.h"
#include "Math/IntRect.h"
#include "RenderResource.h"
#include "RenderingThread.h"
#include "GlobalShader.h"
#include "ShaderParameters.h"
#include "RHICommandList.h"
#include "RHI.h"
#include "RHIResources.h"
#include "RendererInterface.h"
#include "SceneInterface.h"
#include "PrimitiveSceneProxy.h"
#include "MeshPassProcessor.h"
#include "SceneManagement.h"
#include "PrimitiveViewRelevance.h"
#include "Materials/MaterialRenderProxy.h"
#include "StaticMeshResources.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Engine/CanvasRenderTarget2D.h"
#include "Components/SceneCaptureComponent2D.h"
#include "Engine/SceneCapture2D.h"
#include "Camera/CameraComponent.h"
#include "GameFramework/SpringArmComponent.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/Character.h"
#include "GameFramework/CharacterMovementComponent.h"
#include "Components/CapsuleComponent.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "Engine/RendererSettings.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/PhysicsAsset.h"
#include "PhysicsEngine/PhysicsConstraintComponent.h"
#include "PhysicsEngine/RadialForceComponent.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "Field/FieldSystemActor.h"
#include "Field/FieldSystemComponent.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "NiagaraActor.h"
#include "NiagaraComponent.h"
#include "NiagaraSystem.h"
#include "NiagaraEmitter.h"
#include "NiagaraScript.h"
#include "NiagaraParameterCollection.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeInfo.h"
#include "LandscapeLayerInfoObject.h"
#include "LandscapeMaterialInstanceConstant.h"
#include "LandscapeGrassType.h"
#include "LandscapeSplineActor.h"
#include "LandscapeSplineControlPoint.h"
#include "LandscapeSplineSegment.h"
#include "FoliageType.h"
#include "InstancedFoliageActor.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "AI/NavigationSystemBase.h"
#include "NavigationSystem.h"
#include "NavMesh/NavMeshBoundsVolume.h"
#include "NavMesh/RecastNavMesh.h"
#include "NavigationData.h"
#include "Navigation/NavLinkProxy.h"
#include "NavModifierVolume.h"
#include "NavMesh/NavMeshRenderingComponent.h"
#include "AIController.h"
#include "BehaviorTree/BehaviorTree.h"
#include "BehaviorTree/BehaviorTreeComponent.h"
#include "BehaviorTree/BlackboardComponent.h"
#include "BehaviorTree/Blackboard/BlackboardKeyType.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISenseConfig.h"
#include "Perception/AISense_Sight.h"
#include "Perception/AISense_Hearing.h"
#include "Perception/AISense_Touch.h"
#include "Perception/AISense_Damage.h"
#include "Perception/AIPerceptionStimuliSourceComponent.h"
#include "GameplayTasksComponent.h"
#include "GameplayTask.h"
#include "AITypes.h"
#include "Navigation/PathFollowingComponent.h"
#include "Navigation/CrowdFollowingComponent.h"
#include "DetourCrowdAIController.h"
#include "MassEntitySubsystem.h"
#include "MassEntityManager.h"
#include "MassProcessingTypes.h"
#include "MassProcessor.h"
#include "MassMovementFragments.h"
#include "MassCommonFragments.h"
#include "MassRepresentationFragments.h"
#include "MassLODFragments.h"
#include "MassSimulationSubsystem.h"
#include "MassSpawner.h"
#include "MassEntityConfigAsset.h"
// StateTree includes removed - not available in UE 5.6 base installation
// Gameplay Ability System includes removed - not available in UE 5.6 base installation
#include "GameplayTagContainer.h"
#include "GameplayTagsManager.h"
#include "GameplayTagAssetInterface.h"
#include "DataRegistry.h"
#include "DataRegistrySubsystem.h"
#include "DataRegistrySource.h"
#include "Engine/DataTable.h"
#include "Engine/CurveTable.h"
#include "Engine/UserDefinedStruct.h"
#include "Engine/UserDefinedEnum.h"
#include "Engine/Blueprint.h"
#include "Engine/BlueprintGeneratedClass.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "K2Node.h"
#include "K2Node_Event.h"
#include "K2Node_CallFunction.h"
#include "K2Node_VariableGet.h"
#include "K2Node_VariableSet.h"
#include "KismetCompilerModule.h"
#include "ToolMenus.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Styling/AppStyle.h"
#include "Styling/SlateStyle.h"
#include "Brushes/SlateImageBrush.h"
#include "Styling/SlateTypes.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/Input/SSlider.h"
#include "Widgets/Input/SSpinBox.h"
#include "Widgets/Input/SEditableTextBox.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Input/SComboBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Layout/SExpandableArea.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Images/SImage.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STreeView.h"
#include "Widgets/Views/STableViewBase.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Views/SHeaderRow.h"
#include "Framework/Docking/TabManager.h"
#include "Framework/Docking/WorkspaceItem.h"
#include "Widgets/Docking/SDockTab.h"
#include "DesktopPlatformModule.h"
#include "IDesktopPlatform.h"
// HAL/PlatformFileManager.h already included above
// Misc/FileHelper.h already included above
// Misc/Paths.h already included above
// HAL/FileManager.h already included above
#include "GenericPlatform/GenericPlatformFile.h"
#include "Misc/SecureHash.h"
#include "Misc/Base64.h"
#include "Misc/Compression.h"
#include "Serialization/Archive.h"
#include "Serialization/MemoryWriter.h"
#include "Serialization/MemoryReader.h"
#include "Serialization/BufferArchive.h"
#include "Serialization/ObjectAndNameAsStringProxyArchive.h"
#include "UObject/SavePackage.h"
#include "UObject/Package.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/UObjectHash.h"
#include "UObject/UObjectIterator.h"
#include "UObject/PropertyPortFlags.h"
#include "UObject/TextProperty.h"


#include "UObject/EnumProperty.h"
#include "UObject/StrProperty.h"
#include "Templates/Casts.h"
#include "Templates/SubclassOf.h"
#include "Templates/SharedPointer.h"
#include "Templates/UniquePtr.h"
#include "Templates/Function.h"
#include "Templates/Invoke.h"
#include "Templates/UnrealTemplate.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/UnrealString.h"
#include "Containers/StringConv.h"
#include "Internationalization/Text.h"
#include "Internationalization/Internationalization.h"
#include "Internationalization/Culture.h"
#include "Internationalization/Regex.h"
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Vector4.h"
#include "Math/Rotator.h"
#include "Math/Quat.h"
#include "Math/Transform.h"
#include "Math/Matrix.h"
#include "Math/Plane.h"
#include "Math/Box.h"
#include "Math/BoxSphereBounds.h"
#include "Math/Sphere.h"

#include "Math/ConvexHull2d.h"
#include "Math/OrientedBox.h"
#include "Math/Axis.h"
#include "Math/Range.h"
#include "Math/RangeBound.h"
#include "Math/Interval.h"
#include "Math/InterpCurve.h"
#include "Math/CurveEdInterface.h"
#include "Math/Color.h"

#include "Math/NumericLimits.h"
#include "Math/UnrealMathSSE.h"
#include "Math/RandomStream.h"
#include "Misc/Guid.h"
#include "Misc/Crc.h"
#include "Misc/EngineVersion.h"
#include "Misc/NetworkVersion.h"
#include "Misc/CommandLine.h"
#include "Misc/Parse.h"
#include "Misc/DefaultValueHelper.h"
#include "Misc/ExpressionParser.h"
#include "Misc/ExpressionParserTypes.h"
#include "HAL/Platform.h"
#include "HAL/PlatformMisc.h"
#include "HAL/PlatformString.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformAtomics.h"
#include "HAL/PlatformTLS.h"
#include "HAL/ThreadSafeCounter.h"
#include "HAL/ThreadSafeCounter64.h"
#include "HAL/CriticalSection.h"
#include "HAL/Event.h"
#include "HAL/Runnable.h"
#include "HAL/RunnableThread.h"
#include "HAL/ThreadManager.h"
#include "HAL/ThreadHeartBeat.h"
#include "Async/TaskGraphInterfaces.h"
#include "Async/ParallelFor.h"
#include "Async/Async.h"
#include "Async/Future.h"
#include "Stats/Stats.h"
#include "Stats/StatsData.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"
#include "ProfilingDebugging/MemoryTrace.h"
#include "ProfilingDebugging/MiscTrace.h"
#include "Logging/LogMacros.h"
#include "Logging/LogCategory.h"
#include "Logging/LogVerbosity.h"
#include "Logging/StructuredLog.h"
#include "Misc/AssertionMacros.h"
#include "Misc/VarArgs.h"
#include "Misc/Build.h"
#include "Misc/CoreMiscDefines.h"
#include "Misc/CoreDefines.h"
#include "HAL/PreprocessorHelpers.h"
#include "Misc/EnumClassFlags.h"
#include "Templates/IsEnum.h"
#include "Templates/UnrealTypeTraits.h"
#include "Templates/ChooseClass.h"
#include "Templates/EnableIf.h"
#include "Templates/AndOrNot.h"
#include "Templates/AreTypesEqual.h"
#include "Templates/IsAbstract.h"
#include "Templates/IsArithmetic.h"
#include "Templates/IsArray.h"
#include "Templates/IsClass.h"
#include "Templates/IsConst.h"
#include "Templates/IsConstructible.h"
#include "Templates/IsEnum.h"
#include "Templates/IsFloatingPoint.h"
#include "Templates/IsIntegral.h"
#include "Templates/IsPointer.h"
#include "Templates/IsTriviallyCopyConstructible.h"
#include "Templates/IsTriviallyDestructible.h"
#include "Templates/RemoveCV.h"
#include "Templates/RemoveExtent.h"
#include "Templates/RemoveReference.h"
#include "Templates/TypeHash.h"
#include "Templates/Decay.h"
#include "Templates/Greater.h"
#include "Templates/Less.h"
#include "Templates/Sorting.h"
#include "Templates/Tuple.h"
#include "Templates/TypeCompatibleBytes.h"
#include "Templates/AlignmentTemplates.h"
#include "Templates/PointerIsConvertibleFromTo.h"
#include "Templates/IsValidVariadicFunctionArg.h"
#include "Templates/Models.h"
#include "Templates/IdentityFunctor.h"
#include "Templates/Invoke.h"
#include "Templates/ValueOrError.h"
#include "Delegates/Delegate.h"
#include "UObject/WeakObjectPtr.h"
#include "UObject/WeakObjectPtrTemplates.h"
#include "UObject/SoftObjectPtr.h"
#include "UObject/SoftObjectPath.h"
#include "UObject/LazyObjectPtr.h"
#include "UObject/ScriptInterface.h"
#include "UObject/Interface.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Class.h"
#include "UObject/UnrealType.h"
#include "UObject/EnumProperty.h"
#include "UObject/PropertyTag.h"
#include "UObject/Stack.h"
#include "UObject/Script.h"
#include "UObject/MetaData.h"
#include "UObject/FieldPath.h"
#include "UObject/TopLevelAssetPath.h"
#include "UObject/PrimaryAssetId.h"
#include "UObject/ConstructorHelpers.h"
#include "UObject/UObjectAnnotation.h"
#include "UObject/FastReferenceCollector.h"
#include "UObject/GarbageCollection.h"
#include "UObject/ReferenceChainSearch.h"
#include "UObject/ObjectRedirector.h"

#include "UObject/SavePackage.h"
#include "UObject/UObjectThreadContext.h"
#include "UObject/UObjectBase.h"
#include "UObject/UObjectBaseUtility.h"
#include "UObject/Object.h"
#include "UObject/GCObject.h"
#include "UObject/ReferencerFinder.h"


#include "UObject/UnrealNames.h"
#include "UObject/NameTypes.h"
#include "UObject/ScriptMacros.h"
#include "UObject/CoreNative.h"
#include "UObject/TextProperty.h"
#include "UObject/FieldPathProperty.h"


#include "UObject/CoreRedirects.h"

#include "UObject/PropertyOptional.h"

// Object version includes (duplicates removed)

/**
 * Classe responsável por gerenciar comandos do Sistema de Transição de Realms.
 * 
 * Esta classe fornece funcionalidades para:
 * - Criar sistemas de transição entre realms
 * - Configurar streaming de assets
 * - Gerenciar particionamento de mundo
 * - Configurar triggers de transição
 * - Otimizar performance de transições
 * - Debug e validação do sistema
 */
class UNREALMCP_API FUnrealMCPRealmCommands
{
public:
    FUnrealMCPRealmCommands();
    ~FUnrealMCPRealmCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // ========================================================================
    // Métodos Principais do Sistema de Transição de Realms
    // ========================================================================

    /**
     * Cria um sistema de transição de realms multicamada.
     * 
     * @param CommandData Dados do comando contendo configurações dos realms e transições
     * @return Resposta JSON com informações do sistema criado
     */
    TSharedPtr<FJsonObject> HandleCreateRealmTransitionSystem(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura o sistema de streaming de assets para transições.
     * 
     * @param CommandData Dados do comando contendo configurações de streaming
     * @return Resposta JSON com configuração do streaming
     */
    TSharedPtr<FJsonObject> HandleConfigureAssetStreaming(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura o sistema de particionamento de mundo.
     * 
     * @param CommandData Dados do comando contendo configurações de particionamento
     * @return Resposta JSON com configuração do particionamento
     */
    TSharedPtr<FJsonObject> HandleSetupWorldPartitioning(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Cria triggers para transições entre realms.
     * 
     * @param CommandData Dados do comando contendo configurações dos triggers
     * @return Resposta JSON com informações dos triggers criados
     */
    TSharedPtr<FJsonObject> HandleCreateTransitionTriggers(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura a persistência de estado dos realms.
     * 
     * @param CommandData Dados do comando contendo configurações de persistência
     * @return Resposta JSON com configuração de persistência
     */
    TSharedPtr<FJsonObject> HandleConfigureRealmPersistence(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Configura comunicação entre realms.
     * 
     * @param CommandData Dados do comando contendo configurações de comunicação
     * @return Resposta JSON com configuração de comunicação
     */
    TSharedPtr<FJsonObject> HandleSetupCrossRealmCommunication(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Otimiza a performance das transições de realms.
     * 
     * @param CommandData Dados do comando contendo configurações de otimização
     * @return Resposta JSON com resultados da otimização
     */
    TSharedPtr<FJsonObject> HandleOptimizeTransitionPerformance(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Ativa ferramentas de debug para transições de realms.
     * 
     * @param CommandData Dados do comando contendo opções de debug
     * @return Resposta JSON com informações de debug
     */
    TSharedPtr<FJsonObject> HandleDebugRealmTransitions(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Valida a configuração do sistema de transição de realms.
     * 
     * @param CommandData Dados do comando contendo testes de validação
     * @return Resposta JSON com resultados da validação
     */
    TSharedPtr<FJsonObject> HandleValidateRealmSetup(const TSharedPtr<FJsonObject>& CommandData);

    /**
     * Obtém o status do sistema de transição de realms.
     * 
     * @param CommandData Dados do comando (opcional)
     * @return Resposta JSON com status do sistema
     */
    TSharedPtr<FJsonObject> HandleGetRealmSystemStatus(const TSharedPtr<FJsonObject>& CommandData);

    // ========================================================================
    // Constantes Públicas
    // ========================================================================

    // Tipos de resposta
    static const FString RESPONSE_SUCCESS;
    static const FString RESPONSE_ERROR;
    static const FString RESPONSE_WARNING;
    static const FString RESPONSE_INFO;

private:
    // ========================================================================
    // Constantes e Enums Privados
    // ========================================================================

    // Tipos de transição
    static const FString TRANSITION_SEAMLESS;
    static const FString TRANSITION_LOADING_SCREEN;
    static const FString TRANSITION_FADE;
    static const FString TRANSITION_PORTAL;
    static const FString TRANSITION_TELEPORT;

    // Estratégias de streaming
    static const FString STREAMING_PRELOAD_ALL;
    static const FString STREAMING_LAZY_LOADING;
    static const FString STREAMING_PREDICTIVE;
    static const FString STREAMING_DISTANCE_BASED;
    static const FString STREAMING_PRIORITY_BASED;

    // Métodos de particionamento
    static const FString PARTITIONING_GRID_BASED;
    static const FString PARTITIONING_HIERARCHICAL;
    static const FString PARTITIONING_ADAPTIVE;
    static const FString PARTITIONING_CONTENT_AWARE;
    static const FString PARTITIONING_PERFORMANCE_BASED;

    // ========================================================================
    // Funções Auxiliares
    // ========================================================================

    /**
     * Converte array JSON para TArray<FString>.
     */
    TArray<FString> JsonArrayToStringArray(const TArray<TSharedPtr<FJsonValue>>& JsonArray);

    /**
     * Converte TArray<FString> para array JSON.
     */
    TArray<TSharedPtr<FJsonValue>> StringArrayToJsonArray(const TArray<FString>& StringArray);

    /**
     * Converte objeto JSON para TMap<FString, FString>.
     */
    TMap<FString, FString> JsonObjectToStringMap(const TSharedPtr<FJsonObject>& JsonObject);

    /**
     * Converte TMap<FString, FString> para objeto JSON.
     */
    TSharedPtr<FJsonObject> StringMapToJsonObject(const TMap<FString, FString>& StringMap);

    /**
     * Valida configuração de realm.
     */
    bool ValidateRealmConfig(const TSharedPtr<FJsonObject>& RealmConfig, FString& ErrorMessage);

    /**
     * Valida configuração de transição.
     */
    bool ValidateTransitionConfig(const TSharedPtr<FJsonObject>& TransitionConfig, FString& ErrorMessage);

    /**
     * Valida configurações de streaming.
     */
    bool ValidateStreamingConfig(const TSharedPtr<FJsonObject>& StreamingConfig, FString& ErrorMessage);

    /**
     * Valida configurações de particionamento.
     */
    bool ValidatePartitioningConfig(const TSharedPtr<FJsonObject>& PartitioningConfig, FString& ErrorMessage);

    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode = TEXT("REALM_ERROR"));

    /**
     * Cria resposta de sucesso.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message = TEXT("Operation completed successfully"));

    /**
     * Simula criação de sistema de transição.
     */
    TSharedPtr<FJsonObject> SimulateRealmSystemCreation(const FString& LayerName, 
                                                       const TArray<TSharedPtr<FJsonValue>>& RealmConfigs,
                                                       const TArray<TSharedPtr<FJsonValue>>& TransitionConfigs);

    /**
     * Simula configuração de streaming.
     */
    TSharedPtr<FJsonObject> SimulateStreamingConfiguration(const FString& LayerName,
                                                          const FString& Strategy,
                                                          const TSharedPtr<FJsonObject>& AssetPriorities,
                                                          const TSharedPtr<FJsonObject>& BandwidthLimits);

    /**
     * Simula configuração de particionamento.
     */
    TSharedPtr<FJsonObject> SimulatePartitioningConfiguration(const FString& LayerName,
                                                             const FString& Method,
                                                             const TArray<TSharedPtr<FJsonValue>>& PartitionSize,
                                                             float OverlapDistance);

    /**
     * Simula criação de triggers.
     */
    TSharedPtr<FJsonObject> SimulateTriggerCreation(const FString& LayerName,
                                                   const TArray<TSharedPtr<FJsonValue>>& TriggerConfigs);

    /**
     * Simula configuração de persistência.
     */
    TSharedPtr<FJsonObject> SimulatePersistenceConfiguration(const FString& LayerName,
                                                            const TSharedPtr<FJsonObject>& PersistenceSettings);

    /**
     * Simula configuração de comunicação.
     */
    TSharedPtr<FJsonObject> SimulateCommunicationConfiguration(const FString& LayerName,
                                                              const TArray<TSharedPtr<FJsonValue>>& Channels);

    /**
     * Cria otimização básica de performance.
     */
    TSharedPtr<FJsonObject> CreateBasicPerformanceOptimization(const FString& LayerName,
                                                              const TSharedPtr<FJsonObject>& OptimizationSettings);

    /**
     * Atualiza métricas de performance do sistema.
     */
    void UpdatePerformanceMetrics();

    /**
     * Salva estado atual do sistema de realms.
     */
    void SaveSystemState(const FString& LayerName, const TSharedPtr<FJsonObject>& SystemData);

    // ========================================================================
    // Sistema de Analytics e Telemetria
    // ========================================================================

    /**
     * Inicia coleta de dados de analytics para uma camada específica.
     */
    TSharedPtr<FJsonObject> StartAnalyticsCollection(const FString& LayerName,
                                                    const TArray<FString>& MetricTypes);

    /**
     * Para coleta de dados de analytics.
     */
    TSharedPtr<FJsonObject> StopAnalyticsCollection(const FString& LayerName);

    /**
     * Coleta métricas de gameplay em tempo real.
     */
    TSharedPtr<FJsonObject> CollectGameplayMetrics(const FString& LayerName,
                                                  const FString& EventType,
                                                  const TSharedPtr<FJsonObject>& EventData);

    /**
     * Processa dados coletados e gera relatórios.
     */
    TSharedPtr<FJsonObject> ProcessAnalyticsData(const FString& LayerName,
                                                const FString& TimeRange,
                                                const TArray<FString>& MetricFilters);

    /**
     * Gera visualização de dados de analytics.
     */
    TSharedPtr<FJsonObject> GenerateAnalyticsVisualization(const FString& LayerName,
                                                          const FString& VisualizationType,
                                                          const TSharedPtr<FJsonObject>& Parameters);

    /**
     * Configura alertas baseados em métricas.
     */
    TSharedPtr<FJsonObject> ConfigureMetricAlerts(const FString& LayerName,
                                                 const TArray<TSharedPtr<FJsonValue>>& AlertRules);

    /**
     * Exporta dados de analytics para arquivo.
     */
    TSharedPtr<FJsonObject> ExportAnalyticsData(const FString& LayerName,
                                               const FString& ExportFormat,
                                               const FString& FilePath);

    /**
     * Importa dados de analytics de arquivo.
     */
    TSharedPtr<FJsonObject> ImportAnalyticsData(const FString& LayerName,
                                               const FString& FilePath);

    /**
     * Obtém estatísticas de performance do sistema.
     */
    TSharedPtr<FJsonObject> GetPerformanceStatistics(const FString& LayerName,
                                                    const FString& StatType);

    /**
     * Configura coleta de telemetria personalizada.
     */
    TSharedPtr<FJsonObject> ConfigureCustomTelemetry(const FString& LayerName,
                                                    const TSharedPtr<FJsonObject>& TelemetryConfig);

    /**
     * Gera relatório de análise de comportamento do jogador.
     */
    TSharedPtr<FJsonObject> GeneratePlayerBehaviorReport(const FString& LayerName,
                                                        const FString& PlayerId,
                                                        const FString& TimeRange);

    /**
     * Configura dashboard de métricas em tempo real.
     */
    TSharedPtr<FJsonObject> ConfigureRealTimeDashboard(const FString& LayerName,
                                                      const TArray<FString>& MetricWidgets);

    /**
     * Obtém dados do dashboard em tempo real.
     */
    TSharedPtr<FJsonObject> GetRealTimeDashboardData(const FString& LayerName);

    /**
     * Cria dashboard em tempo real.
     */
    TSharedPtr<FJsonObject> CreateRealtimeDashboard(const FString& LayerName, const TArray<FString>& MetricTypes);

    /**
     * Configura telemetria personalizada.
     */
    TSharedPtr<FJsonObject> SetupCustomTelemetry(const FString& LayerName, const TSharedPtr<FJsonObject>& TelemetryConfig);

    /**
     * Configura agregação de dados de múltiplas camadas.
     */
    TSharedPtr<FJsonObject> ConfigureMultiLayerAggregation(const TArray<FString>& LayerNames,
                                                          const TSharedPtr<FJsonObject>& AggregationRules);

    /**
     * Gera relatório de comparação entre camadas.
     */
    TSharedPtr<FJsonObject> GenerateLayerComparisonReport(const TArray<FString>& LayerNames,
                                                         const TArray<FString>& ComparisonMetrics);

    /**
     * Configura machine learning para análise preditiva.
     */
    TSharedPtr<FJsonObject> ConfigurePredictiveAnalytics(const FString& LayerName,
                                                        const TSharedPtr<FJsonObject>& MLConfig);

    /**
     * Executa análise preditiva baseada em dados históricos.
     */
    TSharedPtr<FJsonObject> RunPredictiveAnalysis(const FString& LayerName,
                                                 const FString& PredictionType,
                                                 const TSharedPtr<FJsonObject>& InputData);

    /**
     * Configura sistema de A/B testing.
     */
    TSharedPtr<FJsonObject> ConfigureABTesting(const FString& LayerName,
                                              const TSharedPtr<FJsonObject>& TestConfig);

    /**
     * Obtém resultados de A/B testing.
     */
    TSharedPtr<FJsonObject> GetABTestResults(const FString& LayerName,
                                            const FString& TestId);

    /**
     * Configura sistema de heatmaps para análise espacial.
     */
    TSharedPtr<FJsonObject> ConfigureHeatmapAnalysis(const FString& LayerName,
                                                    const TSharedPtr<FJsonObject>& HeatmapConfig);

    /**
     * Gera heatmap baseado em dados de movimento dos jogadores.
     */
    TSharedPtr<FJsonObject> GenerateMovementHeatmap(const FString& LayerName,
                                                   const FString& TimeRange,
                                                   const TSharedPtr<FJsonObject>& Parameters);

    /**
     * Gera análise de heatmap para uma camada específica.
     */
    TSharedPtr<FJsonObject> GenerateHeatmapAnalysis(const FString& LayerName,
                                                   const FString& HeatmapType,
                                                   const FVector& WorldBounds);

    /**
     * Configura sistema de A/B testing para uma camada.
     */
    TSharedPtr<FJsonObject> SetupABTesting(const FString& LayerName,
                                          const TSharedPtr<FJsonObject>& TestConfig);

    /**
     * Executa análise preditiva para uma camada.
     */
    TSharedPtr<FJsonObject> RunPredictiveAnalysis(const FString& LayerName,
                                                 const FString& PredictionType,
                                                 int32 ForecastDays);

    /**
     * Agrega dados de múltiplas camadas.
     */
    TSharedPtr<FJsonObject> AggregateMultiLayerData(const TArray<FString>& LayerNames,
                                                   const FString& AggregationType);

    // ========================================================================
    // Variáveis Privadas
    // ========================================================================

    /** Métricas de performance do sistema */
    TSharedPtr<FJsonObject> PerformanceMetrics;

    /** Cache de configurações de realms */
    TMap<FString, TSharedPtr<FJsonObject>> RealmConfigCache;

    /** Cache de configurações de transições */
    TMap<FString, TSharedPtr<FJsonObject>> TransitionConfigCache;

    /** Estado atual dos sistemas de realms */
    TMap<FString, TSharedPtr<FJsonObject>> RealmSystemStates;

    /** Configurações globais do sistema */
    TSharedPtr<FJsonObject> GlobalSettings;

    /** Flag para indicar se o sistema está inicializado */
    bool bIsInitialized;

    /** Timestamp da última atualização */
    FDateTime LastUpdateTime;

    /** Cache de dados de analytics por camada */
    TMap<FString, TSharedPtr<FJsonObject>> AnalyticsDataCache;

    /** Configurações de coleta de telemetria */
    TMap<FString, TSharedPtr<FJsonObject>> TelemetryConfigs;

    /** Estado de coleta de analytics por camada */
    TMap<FString, bool> AnalyticsCollectionStates;

    /** Cache de métricas de gameplay */
    TMap<FString, TArray<TSharedPtr<FJsonObject>>> GameplayMetricsCache;

    /** Configurações de alertas por camada */
    TMap<FString, TArray<TSharedPtr<FJsonValue>>> MetricAlertsConfig;

    /** Dados de dashboard em tempo real */
    TMap<FString, TSharedPtr<FJsonObject>> RealTimeDashboardData;

    /** Configurações de telemetria personalizada */
    TMap<FString, TSharedPtr<FJsonObject>> CustomTelemetryConfig;

    /** Configurações de agregação multi-camada */
    TSharedPtr<FJsonObject> MultiLayerAggregationConfig;

    /** Cache de análises preditivas */
    TMap<FString, TSharedPtr<FJsonObject>> PredictiveAnalyticsCache;

    /** Configurações de A/B testing */
    TMap<FString, TSharedPtr<FJsonObject>> ABTestingConfigs;

    /** Dados de heatmap por camada */
    TMap<FString, TSharedPtr<FJsonObject>> HeatmapDataCache;

    /** Timestamp da última coleta de analytics */
    TMap<FString, FDateTime> LastAnalyticsCollectionTime;
};