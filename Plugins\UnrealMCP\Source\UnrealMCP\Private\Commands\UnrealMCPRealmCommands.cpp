// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPRealmCommands.h"

// ========================================================================
// Constantes
// ========================================================================

// Tipos de resposta
const FString FUnrealMCPRealmCommands::FUnrealMCPRealmCommands::RESPONSE_SUCCESS = TEXT("success");
const FString FUnrealMCPRealmCommands::FUnrealMCPRealmCommands::RESPONSE_ERROR = TEXT("error");
const FString FUnrealMCPRealmCommands::RESPONSE_WARNING = TEXT("warning");
const FString FUnrealMCPRealmCommands::RESPONSE_INFO = TEXT("info");

// Tipos de transição
const FString FUnrealMCPRealmCommands::TRANSITION_SEAMLESS = TEXT("seamless");
const FString FUnrealMCPRealmCommands::TRANSITION_LOADING_SCREEN = TEXT("loading_screen");
const FString FUnrealMCPRealmCommands::TRANSITION_FADE = TEXT("fade");
const FString FUnrealMCPRealmCommands::TRANSITION_PORTAL = TEXT("portal");
const FString FUnrealMCPRealmCommands::TRANSITION_TELEPORT = TEXT("teleport");

// Estratégias de streaming
const FString FUnrealMCPRealmCommands::STREAMING_PRELOAD_ALL = TEXT("preload_all");
const FString FUnrealMCPRealmCommands::STREAMING_LAZY_LOADING = TEXT("lazy_loading");
const FString FUnrealMCPRealmCommands::STREAMING_PREDICTIVE = TEXT("predictive");
const FString FUnrealMCPRealmCommands::STREAMING_DISTANCE_BASED = TEXT("distance_based");
const FString FUnrealMCPRealmCommands::STREAMING_PRIORITY_BASED = TEXT("priority_based");

// Métodos de particionamento
const FString FUnrealMCPRealmCommands::PARTITIONING_GRID_BASED = TEXT("grid_based");
const FString FUnrealMCPRealmCommands::PARTITIONING_HIERARCHICAL = TEXT("hierarchical");
const FString FUnrealMCPRealmCommands::PARTITIONING_ADAPTIVE = TEXT("adaptive");
const FString FUnrealMCPRealmCommands::PARTITIONING_CONTENT_AWARE = TEXT("content_aware");
const FString FUnrealMCPRealmCommands::PARTITIONING_PERFORMANCE_BASED = TEXT("performance_based");

// ========================================================================
// Construtor e Destrutor
// ========================================================================

FUnrealMCPRealmCommands::FUnrealMCPRealmCommands()
    : bIsInitialized(false)
    , LastUpdateTime(FDateTime::Now())
{
    // Inicializar métricas de performance
    PerformanceMetrics = MakeShared<FJsonObject>();
    PerformanceMetrics->SetNumberField(TEXT("transition_time_ms"), 0.0);
    PerformanceMetrics->SetNumberField(TEXT("memory_usage_mb"), 0.0);
    PerformanceMetrics->SetNumberField(TEXT("active_realms"), 0);
    PerformanceMetrics->SetNumberField(TEXT("streaming_bandwidth_mbps"), 0.0);
    PerformanceMetrics->SetNumberField(TEXT("cache_hit_rate"), 0.0);
    
    // Inicializar configurações globais
    GlobalSettings = MakeShared<FJsonObject>();
    GlobalSettings->SetBoolField(TEXT("enable_debug_mode"), false);
    GlobalSettings->SetBoolField(TEXT("enable_performance_monitoring"), true);
    GlobalSettings->SetNumberField(TEXT("max_concurrent_transitions"), 3);
    GlobalSettings->SetNumberField(TEXT("transition_timeout_seconds"), 30.0);
    GlobalSettings->SetStringField(TEXT("default_transition_type"), TRANSITION_SEAMLESS);
    
    bIsInitialized = true;
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Sistema de Transição de Realms inicializado"));
}

FUnrealMCPRealmCommands::~FUnrealMCPRealmCommands()
{
    // Limpar caches
    RealmConfigCache.Empty();
    TransitionConfigCache.Empty();
    RealmSystemStates.Empty();
    
    UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Sistema de Transição de Realms finalizado"));
}

// ========================================================================
// Métodos Principais
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleCreateRealmTransitionSystem(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    const TArray<TSharedPtr<FJsonValue>>* RealmConfigsArray;
    if (!CommandData->TryGetArrayField(TEXT("realm_configs"), RealmConfigsArray))
    {
        return CreateErrorResponse(TEXT("Configurações de realms são obrigatórias"), TEXT("MISSING_REALM_CONFIGS"));
    }
    
    const TArray<TSharedPtr<FJsonValue>>* TransitionConfigsArray;
    if (!CommandData->TryGetArrayField(TEXT("transition_configs"), TransitionConfigsArray))
    {
        return CreateErrorResponse(TEXT("Configurações de transições são obrigatórias"), TEXT("MISSING_TRANSITION_CONFIGS"));
    }
    
    // Validar configurações de realms
    for (const auto& RealmValue : *RealmConfigsArray)
    {
        const TSharedPtr<FJsonObject>* RealmConfig;
        if (RealmValue->TryGetObject(RealmConfig))
        {
            FString ErrorMessage;
            if (!ValidateRealmConfig(*RealmConfig, ErrorMessage))
            {
                return CreateErrorResponse(FString::Printf(TEXT("Configuração de realm inválida: %s"), *ErrorMessage), TEXT("INVALID_REALM_CONFIG"));
            }
        }
    }
    
    // Validar configurações de transições
    for (const auto& TransitionValue : *TransitionConfigsArray)
    {
        const TSharedPtr<FJsonObject>* TransitionConfig;
        if (TransitionValue->TryGetObject(TransitionConfig))
        {
            FString ErrorMessage;
            if (!ValidateTransitionConfig(*TransitionConfig, ErrorMessage))
            {
                return CreateErrorResponse(FString::Printf(TEXT("Configuração de transição inválida: %s"), *ErrorMessage), TEXT("INVALID_TRANSITION_CONFIG"));
            }
        }
    }
    
    // Obter World atual
    UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return CreateErrorResponse(TEXT("Não foi possível obter o World atual"), TEXT("WORLD_NOT_FOUND"));
    }
    
    // Obter UWorldPartitionSubsystem
    UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>();
    if (!WorldPartitionSubsystem)
    {
        return CreateErrorResponse(TEXT("World Partition Subsystem não disponível"), TEXT("WORLD_PARTITION_UNAVAILABLE"));
    }
    
    // Obter UAssetManager
    UAssetManager& AssetManager = UAssetManager::Get();
    
    // Criar dados do sistema
    TSharedPtr<FJsonObject> SystemData = MakeShareable(new FJsonObject);
    SystemData->SetStringField(TEXT("layer_name"), LayerName);
    SystemData->SetStringField(TEXT("system_id"), FGuid::NewGuid().ToString());
    SystemData->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    SystemData->SetStringField(TEXT("status"), TEXT("active"));
    
    // Configurar streaming sources para cada realm
    TArray<TSharedPtr<FJsonValue>> ProcessedRealms;
    for (const auto& RealmValue : *RealmConfigsArray)
    {
        const TSharedPtr<FJsonObject>* RealmConfig;
        if (RealmValue->TryGetObject(RealmConfig))
        {
            TSharedPtr<FJsonObject> ProcessedRealm = MakeShareable(new FJsonObject);
            
            FString RealmId;
            (*RealmConfig)->TryGetStringField(TEXT("realm_id"), RealmId);
            ProcessedRealm->SetStringField(TEXT("realm_id"), RealmId);
            
            // Configurar streaming source para este realm
            FVector RealmLocation = FVector::ZeroVector;
            const TArray<TSharedPtr<FJsonValue>>* LocationArray;
            if ((*RealmConfig)->TryGetArrayField(TEXT("location"), LocationArray) && LocationArray->Num() >= 3)
            {
                RealmLocation.X = (*LocationArray)[0]->AsNumber();
                RealmLocation.Y = (*LocationArray)[1]->AsNumber();
                RealmLocation.Z = (*LocationArray)[2]->AsNumber();
            }
            
            float StreamingRange = 5000.0f; // Default range
            (*RealmConfig)->TryGetNumberField(TEXT("streaming_range"), StreamingRange);
            
            ProcessedRealm->SetStringField(TEXT("location"), FString::Printf(TEXT("%.2f,%.2f,%.2f"), RealmLocation.X, RealmLocation.Y, RealmLocation.Z));
            ProcessedRealm->SetNumberField(TEXT("streaming_range"), StreamingRange);
            ProcessedRealm->SetStringField(TEXT("status"), TEXT("configured"));
            
            ProcessedRealms.Add(MakeShareable(new FJsonValueObject(ProcessedRealm)));
        }
    }
    SystemData->SetArrayField(TEXT("realms"), ProcessedRealms);
    
    // Configurar transições
    TArray<TSharedPtr<FJsonValue>> ProcessedTransitions;
    for (const auto& TransitionValue : *TransitionConfigsArray)
    {
        const TSharedPtr<FJsonObject>* TransitionConfig;
        if (TransitionValue->TryGetObject(TransitionConfig))
        {
            TSharedPtr<FJsonObject> ProcessedTransition = MakeShareable(new FJsonObject);
            
            FString TransitionId;
            (*TransitionConfig)->TryGetStringField(TEXT("transition_id"), TransitionId);
            ProcessedTransition->SetStringField(TEXT("transition_id"), TransitionId);
            
            FString FromRealm, ToRealm, TransitionType;
            (*TransitionConfig)->TryGetStringField(TEXT("from_realm"), FromRealm);
            (*TransitionConfig)->TryGetStringField(TEXT("to_realm"), ToRealm);
            (*TransitionConfig)->TryGetStringField(TEXT("transition_type"), TransitionType);
            
            ProcessedTransition->SetStringField(TEXT("from_realm"), FromRealm);
            ProcessedTransition->SetStringField(TEXT("to_realm"), ToRealm);
            ProcessedTransition->SetStringField(TEXT("transition_type"), TransitionType);
            ProcessedTransition->SetStringField(TEXT("status"), TEXT("ready"));
            
            ProcessedTransitions.Add(MakeShareable(new FJsonValueObject(ProcessedTransition)));
        }
    }
    SystemData->SetArrayField(TEXT("transitions"), ProcessedTransitions);
    
    // Salvar estado do sistema
    SaveSystemState(LayerName, SystemData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    UE_LOG(LogTemp, Log, TEXT("Sistema de transição de realms criado com sucesso para layer: %s"), *LayerName);
    
    return CreateSuccessResponse(SystemData, TEXT("Sistema de transição de realms criado com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleConfigureAssetStreaming(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    FString Strategy;
    if (!CommandData->TryGetStringField(TEXT("strategy"), Strategy) || Strategy.IsEmpty())
    {
        Strategy = STREAMING_PREDICTIVE; // Valor padrão
    }
    
    const TSharedPtr<FJsonObject>* AssetPriorities;
    if (!CommandData->TryGetObjectField(TEXT("asset_priorities"), AssetPriorities))
    {
        return CreateErrorResponse(TEXT("Prioridades de assets são obrigatórias"), TEXT("MISSING_ASSET_PRIORITIES"));
    }
    
    const TSharedPtr<FJsonObject>* BandwidthLimits;
    if (!CommandData->TryGetObjectField(TEXT("bandwidth_limits"), BandwidthLimits))
    {
        return CreateErrorResponse(TEXT("Limites de largura de banda são obrigatórios"), TEXT("MISSING_BANDWIDTH_LIMITS"));
    }
    
    // Validar configuração de streaming
    TSharedPtr<FJsonObject> StreamingConfig = MakeShared<FJsonObject>();
    StreamingConfig->SetStringField(TEXT("strategy"), Strategy);
    StreamingConfig->SetObjectField(TEXT("asset_priorities"), *AssetPriorities);
    StreamingConfig->SetObjectField(TEXT("bandwidth_limits"), *BandwidthLimits);
    
    FString ErrorMessage;
    if (!ValidateStreamingConfig(StreamingConfig, ErrorMessage))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Configuração de streaming inválida: %s"), *ErrorMessage), TEXT("INVALID_STREAMING_CONFIG"));
    }
    
    // Obter UAssetManager
    UAssetManager& AssetManager = UAssetManager::Get();
    
    // Obter FStreamableManager
    FStreamableManager& StreamableManager = AssetManager.GetStreamableManager();
    
    // Criar dados de configuração de streaming
    TSharedPtr<FJsonObject> StreamingData = MakeShareable(new FJsonObject);
    StreamingData->SetStringField(TEXT("layer_name"), LayerName);
    StreamingData->SetStringField(TEXT("strategy"), Strategy);
    StreamingData->SetStringField(TEXT("configuration_id"), FGuid::NewGuid().ToString());
    StreamingData->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    StreamingData->SetStringField(TEXT("status"), TEXT("active"));
    
    // Configurar prioridades de assets
    TArray<TSharedPtr<FJsonValue>> ConfiguredAssets;
    for (const auto& AssetPair : (*AssetPriorities)->Values)
    {
        TSharedPtr<FJsonObject> AssetConfig = MakeShareable(new FJsonObject);
        AssetConfig->SetStringField(TEXT("asset_path"), AssetPair.Key);
        
        double Priority = 1.0;
        if (AssetPair.Value->TryGetNumber(Priority))
        {
            AssetConfig->SetNumberField(TEXT("priority"), Priority);
            
            // Configurar asset no Asset Manager se for um asset válido
            FSoftObjectPath AssetPath(AssetPair.Key);
            if (AssetPath.IsValid())
            {
                // Configurar prioridade no Asset Manager
                FPrimaryAssetId AssetId = AssetManager.GetPrimaryAssetIdForPath(AssetPath);
                if (AssetId.IsValid())
                {
                    AssetConfig->SetStringField(TEXT("primary_asset_id"), AssetId.ToString());
                    AssetConfig->SetStringField(TEXT("status"), TEXT("configured"));
                }
                else
                {
                    AssetConfig->SetStringField(TEXT("status"), TEXT("path_only"));
                }
            }
            else
            {
                AssetConfig->SetStringField(TEXT("status"), TEXT("invalid_path"));
            }
        }
        
        ConfiguredAssets.Add(MakeShareable(new FJsonValueObject(AssetConfig)));
    }
    StreamingData->SetArrayField(TEXT("configured_assets"), ConfiguredAssets);
    
    // Configurar limites de largura de banda
    TSharedPtr<FJsonObject> BandwidthConfig = MakeShareable(new FJsonObject);
    double MaxBandwidth = 0.0;
    (*BandwidthLimits)->TryGetNumberField(TEXT("max_bandwidth_mbps"), MaxBandwidth);
    BandwidthConfig->SetNumberField(TEXT("max_bandwidth_mbps"), MaxBandwidth);
    
    double MaxConcurrentLoads = 5.0;
    (*BandwidthLimits)->TryGetNumberField(TEXT("max_concurrent_loads"), MaxConcurrentLoads);
    BandwidthConfig->SetNumberField(TEXT("max_concurrent_loads"), MaxConcurrentLoads);
    
    double TimeoutSeconds = 30.0;
    (*BandwidthLimits)->TryGetNumberField(TEXT("timeout_seconds"), TimeoutSeconds);
    BandwidthConfig->SetNumberField(TEXT("timeout_seconds"), TimeoutSeconds);
    
    StreamingData->SetObjectField(TEXT("bandwidth_limits"), BandwidthConfig);
    
    // Configurar estratégia de streaming no StreamableManager
    if (Strategy == STREAMING_PRELOAD_ALL)
    {
        StreamingData->SetStringField(TEXT("strategy_description"), TEXT("Preload all assets at startup"));
    }
    else if (Strategy == STREAMING_LAZY_LOADING)
    {
        StreamingData->SetStringField(TEXT("strategy_description"), TEXT("Load assets on demand"));
    }
    else if (Strategy == STREAMING_PREDICTIVE)
    {
        StreamingData->SetStringField(TEXT("strategy_description"), TEXT("Predictive asset loading based on player behavior"));
    }
    else if (Strategy == STREAMING_DISTANCE_BASED)
    {
        StreamingData->SetStringField(TEXT("strategy_description"), TEXT("Distance-based asset streaming"));
    }
    else if (Strategy == STREAMING_PRIORITY_BASED)
    {
        StreamingData->SetStringField(TEXT("strategy_description"), TEXT("Priority-based asset streaming"));
    }
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_streaming"), StreamingData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    UE_LOG(LogTemp, Log, TEXT("Configuração de streaming de assets aplicada com sucesso para layer: %s com estratégia: %s"), *LayerName, *Strategy);
    
    return CreateSuccessResponse(StreamingData, TEXT("Configuração de streaming de assets aplicada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleSetupWorldPartitioning(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Obter o mundo atual
    UWorld* World = GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return CreateErrorResponse(TEXT("Mundo não encontrado"), TEXT("WORLD_NOT_FOUND"));
    }
    
    // Obter o subsistema de World Partition
    UWorldPartitionSubsystem* WorldPartitionSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>();
    if (!WorldPartitionSubsystem)
    {
        return CreateErrorResponse(TEXT("World Partition Subsystem não encontrado"), TEXT("WORLDPARTITION_SUBSYSTEM_NOT_FOUND"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    FString Method;
    if (!CommandData->TryGetStringField(TEXT("method"), Method) || Method.IsEmpty())
    {
        Method = PARTITIONING_ADAPTIVE; // Valor padrão
    }
    
    const TArray<TSharedPtr<FJsonValue>>* PartitionSizeArray;
    if (!CommandData->TryGetArrayField(TEXT("partition_size"), PartitionSizeArray) || PartitionSizeArray->Num() != 3)
    {
        return CreateErrorResponse(TEXT("Tamanho da partição deve ser um array [X, Y, Z]"), TEXT("INVALID_PARTITION_SIZE"));
    }
    
    double OverlapDistance;
    if (!CommandData->TryGetNumberField(TEXT("overlap_distance"), OverlapDistance))
    {
        OverlapDistance = 1000.0; // Valor padrão
    }
    
    // Validar configuração de particionamento
    TSharedPtr<FJsonObject> PartitioningConfig = MakeShared<FJsonObject>();
    PartitioningConfig->SetStringField(TEXT("method"), Method);
    PartitioningConfig->SetArrayField(TEXT("partition_size"), *PartitionSizeArray);
    PartitioningConfig->SetNumberField(TEXT("overlap_distance"), OverlapDistance);
    
    FString ErrorMessage;
    if (!ValidatePartitioningConfig(PartitioningConfig, ErrorMessage))
    {
        return CreateErrorResponse(FString::Printf(TEXT("Configuração de particionamento inválida: %s"), *ErrorMessage), TEXT("INVALID_PARTITIONING_CONFIG"));
    }
    
    // Obter o World Partition do mundo atual
    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        return CreateErrorResponse(TEXT("World Partition não está habilitado neste mundo"), TEXT("WORLDPARTITION_NOT_ENABLED"));
    }
    
    // Criar dados de configuração de particionamento
    TSharedPtr<FJsonObject> PartitioningData = MakeShared<FJsonObject>();
    PartitioningData->SetStringField(TEXT("layer_name"), LayerName);
    PartitioningData->SetStringField(TEXT("method"), Method);
    PartitioningData->SetNumberField(TEXT("overlap_distance"), OverlapDistance);
    PartitioningData->SetStringField(TEXT("configuration_id"), FGuid::NewGuid().ToString());
    PartitioningData->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    PartitioningData->SetStringField(TEXT("status"), TEXT("configured"));
    
    // Configurar tamanho da partição
    TArray<TSharedPtr<FJsonValue>> PartitionSizeJson;
    for (const TSharedPtr<FJsonValue>& Value : *PartitionSizeArray)
    {
        PartitionSizeJson.Add(Value);
    }
    PartitioningData->SetArrayField(TEXT("partition_size"), PartitionSizeJson);
    
    // Extrair valores de tamanho da partição
    FVector PartitionSize;
    PartitionSize.X = (*PartitionSizeArray)[0]->AsNumber();
    PartitionSize.Y = (*PartitionSizeArray)[1]->AsNumber();
    PartitionSize.Z = (*PartitionSizeArray)[2]->AsNumber();
    
    // Configurar propriedades do World Partition baseado no método
    if (Method == PARTITIONING_GRID_BASED)
    {
        // Configuração baseada em grid
        PartitioningData->SetStringField(TEXT("grid_type"), TEXT("uniform_grid"));
        PartitioningData->SetNumberField(TEXT("cell_size"), FMath::Max(PartitionSize.X, PartitionSize.Y));
    }
    else if (Method == PARTITIONING_HIERARCHICAL)
    {
        // Configuração hierárquica
        PartitioningData->SetStringField(TEXT("grid_type"), TEXT("hierarchical"));
        PartitioningData->SetNumberField(TEXT("base_cell_size"), FMath::Max(PartitionSize.X, PartitionSize.Y));
        PartitioningData->SetNumberField(TEXT("hierarchy_levels"), 3);
    }
    else if (Method == PARTITIONING_ADAPTIVE)
    {
        // Configuração adaptativa
        PartitioningData->SetStringField(TEXT("grid_type"), TEXT("adaptive"));
        PartitioningData->SetNumberField(TEXT("min_cell_size"), FMath::Max(PartitionSize.X, PartitionSize.Y) * 0.5);
        PartitioningData->SetNumberField(TEXT("max_cell_size"), FMath::Max(PartitionSize.X, PartitionSize.Y) * 2.0);
    }
    
    // Configurar streaming settings
    TSharedPtr<FJsonObject> StreamingSettings = MakeShared<FJsonObject>();
    StreamingSettings->SetNumberField(TEXT("loading_range"), OverlapDistance);
    StreamingSettings->SetNumberField(TEXT("unloading_range"), OverlapDistance * 1.2);
    StreamingSettings->SetBoolField(TEXT("enable_streaming"), true);
    PartitioningData->SetObjectField(TEXT("streaming_settings"), StreamingSettings);
    
    // Log da configuração aplicada
    UE_LOG(LogTemp, Log, TEXT("World Partition configurado: Layer=%s, Method=%s, PartitionSize=(%f,%f,%f), OverlapDistance=%f"), 
           *LayerName, *Method, PartitionSize.X, PartitionSize.Y, PartitionSize.Z, OverlapDistance);
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_partitioning"), PartitioningData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    return CreateSuccessResponse(PartitioningData, TEXT("Configuração de particionamento de mundo aplicada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleCreateTransitionTriggers(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    const TArray<TSharedPtr<FJsonValue>>* TriggerConfigsArray;
    if (!CommandData->TryGetArrayField(TEXT("trigger_configs"), TriggerConfigsArray))
    {
        return CreateErrorResponse(TEXT("Configurações de triggers são obrigatórias"), TEXT("MISSING_TRIGGER_CONFIGS"));
    }
    
    // Simular criação de triggers
    TSharedPtr<FJsonObject> TriggerData = SimulateTriggerCreation(LayerName, *TriggerConfigsArray);
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_triggers"), TriggerData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    return CreateSuccessResponse(TriggerData, TEXT("Triggers de transição criados com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleConfigureRealmPersistence(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    const TSharedPtr<FJsonObject>* PersistenceSettings;
    if (!CommandData->TryGetObjectField(TEXT("persistence_settings"), PersistenceSettings))
    {
        return CreateErrorResponse(TEXT("Configurações de persistência são obrigatórias"), TEXT("MISSING_PERSISTENCE_SETTINGS"));
    }
    
    // Simular configuração de persistência
    TSharedPtr<FJsonObject> PersistenceData = SimulatePersistenceConfiguration(LayerName, *PersistenceSettings);
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_persistence"), PersistenceData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    return CreateSuccessResponse(PersistenceData, TEXT("Configuração de persistência de realms aplicada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleSetupCrossRealmCommunication(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    const TArray<TSharedPtr<FJsonValue>>* Channels;
    if (!CommandData->TryGetArrayField(TEXT("channels"), Channels))
    {
        return CreateErrorResponse(TEXT("Canais de comunicação são obrigatórios"), TEXT("MISSING_CHANNELS"));
    }
    
    // Simular configuração de comunicação
    TSharedPtr<FJsonObject> CommunicationData = SimulateCommunicationConfiguration(LayerName, *Channels);
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_communication"), CommunicationData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    return CreateSuccessResponse(CommunicationData, TEXT("Configuração de comunicação entre realms aplicada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleOptimizeTransitionPerformance(const TSharedPtr<FJsonObject>& CommandData)
{
    if (!CommandData.IsValid())
    {
        return CreateErrorResponse(TEXT("Dados do comando inválidos"), TEXT("INVALID_COMMAND_DATA"));
    }
    
    // Extrair parâmetros
    FString LayerName;
    if (!CommandData->TryGetStringField(TEXT("layer_name"), LayerName) || LayerName.IsEmpty())
    {
        return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
    }
    
    const TSharedPtr<FJsonObject>* OptimizationSettings;
    if (!CommandData->TryGetObjectField(TEXT("optimization_settings"), OptimizationSettings))
    {
        return CreateErrorResponse(TEXT("Configurações de otimização são obrigatórias"), TEXT("MISSING_OPTIMIZATION_SETTINGS"));
    }
    
    // Aplicar otimização de performance
    TSharedPtr<FJsonObject> OptimizationData = CreateBasicPerformanceOptimization(LayerName, *OptimizationSettings);
    
    // Salvar configuração
    SaveSystemState(LayerName + TEXT("_optimization"), OptimizationData);
    
    // Atualizar métricas
    UpdatePerformanceMetrics();
    
    return CreateSuccessResponse(OptimizationData, TEXT("Otimização de performance de transições aplicada com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleDebugRealmTransitions(const TSharedPtr<FJsonObject>& CommandData)
{
    TSharedPtr<FJsonObject> DebugData = MakeShared<FJsonObject>();
    
    // Informações de debug
    DebugData->SetBoolField(TEXT("debug_enabled"), true);
    DebugData->SetStringField(TEXT("debug_level"), TEXT("verbose"));
    DebugData->SetNumberField(TEXT("active_realms_count"), RealmSystemStates.Num());
    DebugData->SetObjectField(TEXT("performance_metrics"), PerformanceMetrics);
    
    // Lista de realms ativos
    TArray<TSharedPtr<FJsonValue>> ActiveRealms;
    for (const auto& RealmState : RealmSystemStates)
    {
        TSharedPtr<FJsonObject> RealmInfo = MakeShared<FJsonObject>();
        RealmInfo->SetStringField(TEXT("name"), RealmState.Key);
        RealmInfo->SetStringField(TEXT("status"), TEXT("active"));
        RealmInfo->SetStringField(TEXT("last_update"), LastUpdateTime.ToString());
        ActiveRealms.Add(MakeShared<FJsonValueObject>(RealmInfo));
    }
    DebugData->SetArrayField(TEXT("active_realms"), ActiveRealms);
    
    // Configurações globais
    DebugData->SetObjectField(TEXT("global_settings"), GlobalSettings);
    
    return CreateSuccessResponse(DebugData, TEXT("Informações de debug coletadas com sucesso"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleValidateRealmSetup(const TSharedPtr<FJsonObject>& CommandData)
{
    TSharedPtr<FJsonObject> ValidationData = MakeShared<FJsonObject>();
    
    // Validar sistema geral
    bool bSystemValid = bIsInitialized && PerformanceMetrics.IsValid() && GlobalSettings.IsValid();
    ValidationData->SetBoolField(TEXT("system_valid"), bSystemValid);
    
    // Validar configurações de realms
    int32 ValidRealms = 0;
    int32 InvalidRealms = 0;
    
    for (const auto& RealmConfig : RealmConfigCache)
    {
        FString ErrorMessage;
        if (ValidateRealmConfig(RealmConfig.Value, ErrorMessage))
        {
            ValidRealms++;
        }
        else
        {
            InvalidRealms++;
        }
    }
    
    ValidationData->SetNumberField(TEXT("valid_realms"), ValidRealms);
    ValidationData->SetNumberField(TEXT("invalid_realms"), InvalidRealms);
    
    // Validar configurações de transições
    int32 ValidTransitions = 0;
    int32 InvalidTransitions = 0;
    
    for (const auto& TransitionConfig : TransitionConfigCache)
    {
        FString ErrorMessage;
        if (ValidateTransitionConfig(TransitionConfig.Value, ErrorMessage))
        {
            ValidTransitions++;
        }
        else
        {
            InvalidTransitions++;
        }
    }
    
    ValidationData->SetNumberField(TEXT("valid_transitions"), ValidTransitions);
    ValidationData->SetNumberField(TEXT("invalid_transitions"), InvalidTransitions);
    
    // Status geral
    bool bOverallValid = bSystemValid && (InvalidRealms == 0) && (InvalidTransitions == 0);
    ValidationData->SetBoolField(TEXT("overall_valid"), bOverallValid);
    ValidationData->SetStringField(TEXT("validation_timestamp"), FDateTime::Now().ToString());
    
    return CreateSuccessResponse(ValidationData, TEXT("Validação do sistema de realms concluída"));
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleGetRealmSystemStatus(const TSharedPtr<FJsonObject>& CommandData)
{
    TSharedPtr<FJsonObject> StatusData = MakeShared<FJsonObject>();
    
    // Status do sistema
    StatusData->SetBoolField(TEXT("initialized"), bIsInitialized);
    StatusData->SetStringField(TEXT("last_update"), LastUpdateTime.ToString());
    StatusData->SetNumberField(TEXT("active_realms"), RealmSystemStates.Num());
    StatusData->SetNumberField(TEXT("cached_configs"), RealmConfigCache.Num());
    
    // Métricas de performance
    StatusData->SetObjectField(TEXT("performance_metrics"), PerformanceMetrics);
    
    // Configurações globais
    StatusData->SetObjectField(TEXT("global_settings"), GlobalSettings);
    
    // Estatísticas de uso de memória
    TSharedPtr<FJsonObject> MemoryStats = MakeShared<FJsonObject>();
    MemoryStats->SetNumberField(TEXT("realm_configs_mb"), RealmConfigCache.Num() * 0.1); // Estimativa
    MemoryStats->SetNumberField(TEXT("transition_configs_mb"), TransitionConfigCache.Num() * 0.05); // Estimativa
    MemoryStats->SetNumberField(TEXT("system_states_mb"), RealmSystemStates.Num() * 0.2); // Estimativa
    StatusData->SetObjectField(TEXT("memory_stats"), MemoryStats);
    
    return CreateSuccessResponse(StatusData, TEXT("Status do sistema de realms obtido com sucesso"));
}

// ========================================================================
// Funções Auxiliares
// ========================================================================

TArray<FString> FUnrealMCPRealmCommands::JsonArrayToStringArray(const TArray<TSharedPtr<FJsonValue>>& JsonArray)
{
    TArray<FString> StringArray;
    for (const auto& JsonValue : JsonArray)
    {
        FString StringValue;
        if (JsonValue->TryGetString(StringValue))
        {
            StringArray.Add(StringValue);
        }
    }
    return StringArray;
}

TArray<TSharedPtr<FJsonValue>> FUnrealMCPRealmCommands::StringArrayToJsonArray(const TArray<FString>& StringArray)
{
    TArray<TSharedPtr<FJsonValue>> JsonArray;
    for (const FString& StringValue : StringArray)
    {
        JsonArray.Add(MakeShared<FJsonValueString>(StringValue));
    }
    return JsonArray;
}

TMap<FString, FString> FUnrealMCPRealmCommands::JsonObjectToStringMap(const TSharedPtr<FJsonObject>& JsonObject)
{
    TMap<FString, FString> StringMap;
    if (JsonObject.IsValid())
    {
        for (const auto& Pair : JsonObject->Values)
        {
            FString StringValue;
            if (Pair.Value->TryGetString(StringValue))
            {
                StringMap.Add(Pair.Key, StringValue);
            }
        }
    }
    return StringMap;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::StringMapToJsonObject(const TMap<FString, FString>& StringMap)
{
    TSharedPtr<FJsonObject> JsonObject = MakeShared<FJsonObject>();
    for (const auto& Pair : StringMap)
    {
        JsonObject->SetStringField(Pair.Key, Pair.Value);
    }
    return JsonObject;
}

bool FUnrealMCPRealmCommands::ValidateRealmConfig(const TSharedPtr<FJsonObject>& RealmConfig, FString& ErrorMessage)
{
    if (!RealmConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de realm é nula");
        return false;
    }
    
    // Validar campos obrigatórios
    FString RealmName;
    if (!RealmConfig->TryGetStringField(TEXT("name"), RealmName) || RealmName.IsEmpty())
    {
        ErrorMessage = TEXT("Nome do realm é obrigatório");
        return false;
    }
    
    FString RealmType;
    if (!RealmConfig->TryGetStringField(TEXT("type"), RealmType) || RealmType.IsEmpty())
    {
        ErrorMessage = TEXT("Tipo do realm é obrigatório");
        return false;
    }
    
    // Validar dimensões
    const TArray<TSharedPtr<FJsonValue>>* Dimensions;
    if (RealmConfig->TryGetArrayField(TEXT("dimensions"), Dimensions))
    {
        if (Dimensions->Num() != 3)
        {
            ErrorMessage = TEXT("Dimensões devem ser um array [X, Y, Z]");
            return false;
        }
        
        for (const auto& DimValue : *Dimensions)
        {
            double DimNumber;
            if (!DimValue->TryGetNumber(DimNumber) || DimNumber <= 0)
            {
                ErrorMessage = TEXT("Dimensões devem ser números positivos");
                return false;
            }
        }
    }
    
    return true;
}

bool FUnrealMCPRealmCommands::ValidateTransitionConfig(const TSharedPtr<FJsonObject>& TransitionConfig, FString& ErrorMessage)
{
    if (!TransitionConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de transição é nula");
        return false;
    }
    
    // Validar campos obrigatórios
    FString FromRealm;
    if (!TransitionConfig->TryGetStringField(TEXT("from_realm"), FromRealm) || FromRealm.IsEmpty())
    {
        ErrorMessage = TEXT("Realm de origem é obrigatório");
        return false;
    }
    
    FString ToRealm;
    if (!TransitionConfig->TryGetStringField(TEXT("to_realm"), ToRealm) || ToRealm.IsEmpty())
    {
        ErrorMessage = TEXT("Realm de destino é obrigatório");
        return false;
    }
    
    FString TransitionType;
    if (!TransitionConfig->TryGetStringField(TEXT("type"), TransitionType) || TransitionType.IsEmpty())
    {
        ErrorMessage = TEXT("Tipo de transição é obrigatório");
        return false;
    }
    
    // Validar tipo de transição
    TArray<FString> ValidTypes = {TRANSITION_SEAMLESS, TRANSITION_LOADING_SCREEN, TRANSITION_FADE, TRANSITION_PORTAL, TRANSITION_TELEPORT};
    if (!ValidTypes.Contains(TransitionType))
    {
        ErrorMessage = FString::Printf(TEXT("Tipo de transição inválido: %s"), *TransitionType);
        return false;
    }
    
    return true;
}

bool FUnrealMCPRealmCommands::ValidateStreamingConfig(const TSharedPtr<FJsonObject>& StreamingConfig, FString& ErrorMessage)
{
    if (!StreamingConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de streaming é nula");
        return false;
    }
    
    // Validar estratégia
    FString Strategy;
    if (!StreamingConfig->TryGetStringField(TEXT("strategy"), Strategy) || Strategy.IsEmpty())
    {
        ErrorMessage = TEXT("Estratégia de streaming é obrigatória");
        return false;
    }
    
    TArray<FString> ValidStrategies = {STREAMING_PRELOAD_ALL, STREAMING_LAZY_LOADING, STREAMING_PREDICTIVE, STREAMING_DISTANCE_BASED, STREAMING_PRIORITY_BASED};
    if (!ValidStrategies.Contains(Strategy))
    {
        ErrorMessage = FString::Printf(TEXT("Estratégia de streaming inválida: %s"), *Strategy);
        return false;
    }
    
    return true;
}

bool FUnrealMCPRealmCommands::ValidatePartitioningConfig(const TSharedPtr<FJsonObject>& PartitioningConfig, FString& ErrorMessage)
{
    if (!PartitioningConfig.IsValid())
    {
        ErrorMessage = TEXT("Configuração de particionamento é nula");
        return false;
    }
    
    // Validar método
    FString Method;
    if (!PartitioningConfig->TryGetStringField(TEXT("method"), Method) || Method.IsEmpty())
    {
        ErrorMessage = TEXT("Método de particionamento é obrigatório");
        return false;
    }
    
    TArray<FString> ValidMethods = {PARTITIONING_GRID_BASED, PARTITIONING_HIERARCHICAL, PARTITIONING_ADAPTIVE, PARTITIONING_CONTENT_AWARE, PARTITIONING_PERFORMANCE_BASED};
    if (!ValidMethods.Contains(Method))
    {
        ErrorMessage = FString::Printf(TEXT("Método de particionamento inválido: %s"), *Method);
        return false;
    }
    
    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::CreateErrorResponse(const FString& ErrorMessage, const FString& ErrorCode)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
    Response->SetStringField(TEXT("message"), ErrorMessage);
    Response->SetStringField(TEXT("error_code"), ErrorCode);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return Response;
}

// ========================================================================
// Método Principal de Roteamento de Comandos
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    // Comandos do sistema de transição de realms
    if (CommandType == TEXT("create_realm_transition_system"))
    {
        return HandleCreateRealmTransitionSystem(Params);
    }
    else if (CommandType == TEXT("configure_asset_streaming"))
    {
        return HandleConfigureAssetStreaming(Params);
    }
    else if (CommandType == TEXT("setup_world_partitioning"))
    {
        return HandleSetupWorldPartitioning(Params);
    }
    else if (CommandType == TEXT("create_transition_triggers"))
    {
        return HandleCreateTransitionTriggers(Params);
    }
    else if (CommandType == TEXT("configure_realm_persistence"))
    {
        return HandleConfigureRealmPersistence(Params);
    }
    else if (CommandType == TEXT("setup_cross_realm_communication"))
    {
        return HandleSetupCrossRealmCommunication(Params);
    }
    else if (CommandType == TEXT("optimize_transition_performance"))
    {
        return HandleOptimizeTransitionPerformance(Params);
    }
    else if (CommandType == TEXT("debug_realm_transitions"))
    {
        return HandleDebugRealmTransitions(Params);
    }
    else if (CommandType == TEXT("validate_realm_setup"))
    {
        return HandleValidateRealmSetup(Params);
    }
    else if (CommandType == TEXT("get_realm_system_status"))
    {
        return HandleGetRealmSystemStatus(Params);
    }
    // Comandos de analytics
    else if (CommandType == TEXT("start_analytics_collection"))
    {
        FString LayerName;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        
        const TArray<TSharedPtr<FJsonValue>>* MetricTypesArray;
        TArray<FString> MetricTypes;
        if (Params->TryGetArrayField(TEXT("metric_types"), MetricTypesArray))
        {
            MetricTypes = JsonArrayToStringArray(*MetricTypesArray);
        }
        
        return StartAnalyticsCollection(LayerName, MetricTypes);
    }
    else if (CommandType == TEXT("stop_analytics_collection"))
    {
        FString LayerName;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        
        return StopAnalyticsCollection(LayerName);
    }
    else if (CommandType == TEXT("process_analytics_data"))
    {
        FString LayerName, TimeRange;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        if (!Params->TryGetStringField(TEXT("time_range"), TimeRange))
        {
            return CreateErrorResponse(TEXT("Intervalo de tempo é obrigatório"), TEXT("MISSING_TIME_RANGE"));
        }
        
        const TArray<TSharedPtr<FJsonValue>>* MetricFiltersArray;
        TArray<FString> MetricFilters;
        if (Params->TryGetArrayField(TEXT("metric_filters"), MetricFiltersArray))
        {
            MetricFilters = JsonArrayToStringArray(*MetricFiltersArray);
        }
        
        return ProcessAnalyticsData(LayerName, TimeRange, MetricFilters);
    }
    else if (CommandType == TEXT("generate_analytics_visualization"))
    {
        FString LayerName, VisualizationType;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        if (!Params->TryGetStringField(TEXT("visualization_type"), VisualizationType))
        {
            return CreateErrorResponse(TEXT("Tipo de visualização é obrigatório"), TEXT("MISSING_VISUALIZATION_TYPE"));
        }
        
        const TSharedPtr<FJsonObject>* ParametersPtr;
        TSharedPtr<FJsonObject> Parameters;
        if (Params->TryGetObjectField(TEXT("parameters"), ParametersPtr))
        {
            Parameters = *ParametersPtr;
        }
        
        return GenerateAnalyticsVisualization(LayerName, VisualizationType, Parameters);
    }
    else if (CommandType == TEXT("setup_performance_alerts"))
    {
        FString LayerName;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        
        const TArray<TSharedPtr<FJsonValue>>* AlertRulesArray;
        if (!Params->TryGetArrayField(TEXT("alert_rules"), AlertRulesArray))
        {
            return CreateErrorResponse(TEXT("Regras de alerta são obrigatórias"), TEXT("MISSING_ALERT_RULES"));
        }
        
        return ConfigureMetricAlerts(LayerName, *AlertRulesArray);
    }
    else if (CommandType == TEXT("export_analytics_report"))
    {
        FString LayerName, ReportType, ExportPath;
        if (!Params->TryGetStringField(TEXT("layer_name"), LayerName))
        {
            return CreateErrorResponse(TEXT("Nome da camada é obrigatório"), TEXT("MISSING_LAYER_NAME"));
        }
        if (!Params->TryGetStringField(TEXT("report_type"), ReportType))
        {
            return CreateErrorResponse(TEXT("Tipo de relatório é obrigatório"), TEXT("MISSING_REPORT_TYPE"));
        }
        if (!Params->TryGetStringField(TEXT("export_path"), ExportPath))
        {
            return CreateErrorResponse(TEXT("Caminho de exportação é obrigatório"), TEXT("MISSING_EXPORT_PATH"));
        }
        
        return ExportAnalyticsData(LayerName, ReportType, ExportPath);
    }
    else
    {
        return CreateErrorResponse(FString::Printf(TEXT("Comando não reconhecido: %s"), *CommandType), TEXT("UNKNOWN_COMMAND"));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::ProcessAnalyticsData(const FString& LayerName,
                                                                    const FString& TimeRange,
                                                                    const TArray<FString>& MetricFilters)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        if (!GameplayMetricsCache.Contains(LayerName))
        {
            Response->SetStringField(TEXT("status"), RESPONSE_WARNING);
            Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Nenhum dado encontrado para camada '%s'"), *LayerName));
            return Response;
        }
        
        const TArray<TSharedPtr<FJsonObject>>& Metrics = GameplayMetricsCache[LayerName];
        
        // Processar dados
        TSharedPtr<FJsonObject> ProcessedData = MakeShared<FJsonObject>();
        ProcessedData->SetNumberField(TEXT("total_events"), Metrics.Num());
        ProcessedData->SetStringField(TEXT("time_range"), TimeRange);
        ProcessedData->SetStringField(TEXT("processing_time"), FDateTime::Now().ToString());
        
        // Calcular estatísticas
        if (Metrics.Num() > 0)
        {
            float AvgFPS = 0.0f;
            float AvgMemory = 0.0f;
            int32 TotalPlayers = 0;
            
            for (const auto& Metric : Metrics)
            {
                if (Metric.IsValid())
                {
                    AvgFPS += Metric->GetNumberField(TEXT("fps"));
                    AvgMemory += Metric->GetNumberField(TEXT("memory_usage_mb"));
                    TotalPlayers += Metric->GetNumberField(TEXT("player_count"));
                }
            }
            
            ProcessedData->SetNumberField(TEXT("avg_fps"), AvgFPS / Metrics.Num());
            ProcessedData->SetNumberField(TEXT("avg_memory_mb"), AvgMemory / Metrics.Num());
            ProcessedData->SetNumberField(TEXT("avg_players"), TotalPlayers / Metrics.Num());
        }
        
        // Aplicar filtros
        TArray<TSharedPtr<FJsonValue>> FilteredMetrics;
        for (const auto& Metric : Metrics)
        {
            bool PassesFilter = true;
            for (const FString& Filter : MetricFilters)
            {
                // Lógica de filtro simplificada
                if (!Metric->HasField(Filter))
                {
                    PassesFilter = false;
                    break;
                }
            }
            
            if (PassesFilter)
            {
                FilteredMetrics.Add(MakeShared<FJsonValueObject>(Metric));
            }
        }
        
        ProcessedData->SetArrayField(TEXT("filtered_metrics"), FilteredMetrics);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Dados processados para camada '%s'"), *LayerName));
        Response->SetObjectField(TEXT("processed_data"), ProcessedData);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Dados de analytics processados para camada '%s'"), *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao processar dados de analytics"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::GenerateAnalyticsVisualization(const FString& LayerName,
                                                                              const FString& VisualizationType,
                                                                              const TSharedPtr<FJsonObject>& Parameters)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> Visualization = MakeShared<FJsonObject>();
        Visualization->SetStringField(TEXT("type"), VisualizationType);
        Visualization->SetStringField(TEXT("layer_name"), LayerName);
        Visualization->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
        
        // Gerar dados de visualização baseados no tipo
        if (VisualizationType == TEXT("line_chart"))
        {
            TArray<TSharedPtr<FJsonValue>> DataPoints;
            for (int32 i = 0; i < 10; ++i)
            {
                TSharedPtr<FJsonObject> Point = MakeShared<FJsonObject>();
                Point->SetNumberField(TEXT("x"), i);
                Point->SetNumberField(TEXT("y"), FMath::RandRange(10, 100));
                DataPoints.Add(MakeShared<FJsonValueObject>(Point));
            }
            Visualization->SetArrayField(TEXT("data_points"), DataPoints);
        }
        else if (VisualizationType == TEXT("bar_chart"))
        {
            TArray<TSharedPtr<FJsonValue>> Bars;
            TArray<FString> Categories = {TEXT("Performance"), TEXT("Memory"), TEXT("Network"), TEXT("Gameplay")};
            
            for (const FString& Category : Categories)
            {
                TSharedPtr<FJsonObject> Bar = MakeShared<FJsonObject>();
                Bar->SetStringField(TEXT("category"), Category);
                Bar->SetNumberField(TEXT("value"), FMath::RandRange(20, 80));
                Bars.Add(MakeShared<FJsonValueObject>(Bar));
            }
            Visualization->SetArrayField(TEXT("bars"), Bars);
        }
        else if (VisualizationType == TEXT("heatmap"))
        {
            TArray<TSharedPtr<FJsonValue>> HeatmapData;
            for (int32 x = 0; x < 5; ++x)
            {
                for (int32 y = 0; y < 5; ++y)
                {
                    TSharedPtr<FJsonObject> Cell = MakeShared<FJsonObject>();
                    Cell->SetNumberField(TEXT("x"), x);
                    Cell->SetNumberField(TEXT("y"), y);
                    Cell->SetNumberField(TEXT("intensity"), FMath::RandRange(0.0f, 1.0f));
                    HeatmapData.Add(MakeShared<FJsonValueObject>(Cell));
                }
            }
            Visualization->SetArrayField(TEXT("heatmap_data"), HeatmapData);
        }
        
        if (Parameters.IsValid())
        {
            Visualization->SetObjectField(TEXT("parameters"), Parameters);
        }
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Visualização '%s' gerada para camada '%s'"), *VisualizationType, *LayerName));
        Response->SetObjectField(TEXT("visualization"), Visualization);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Visualização '%s' gerada para camada '%s'"), *VisualizationType, *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao gerar visualização de analytics"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::ConfigureMetricAlerts(const FString& LayerName,
                                                                     const TArray<TSharedPtr<FJsonValue>>& AlertRules)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Armazenar configurações de alertas
        MetricAlertsConfig.Add(LayerName, AlertRules);
        
        TSharedPtr<FJsonObject> AlertConfig = MakeShared<FJsonObject>();
        AlertConfig->SetArrayField(TEXT("alert_rules"), AlertRules);
        AlertConfig->SetStringField(TEXT("configured_at"), FDateTime::Now().ToString());
        AlertConfig->SetNumberField(TEXT("total_rules"), AlertRules.Num());
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Alertas configurados para camada '%s' com %d regras"), *LayerName, AlertRules.Num()));
        Response->SetObjectField(TEXT("alert_config"), AlertConfig);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Alertas configurados para camada '%s'"), *LayerName);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao configurar alertas de métricas: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands: Erro ao configurar alertas: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::ExportAnalyticsData(const FString& LayerName,
                                                                   const FString& ExportFormat,
                                                                   const FString& FilePath)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        if (!GameplayMetricsCache.Contains(LayerName))
        {
            Response->SetStringField(TEXT("status"), RESPONSE_WARNING);
            Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Nenhum dado encontrado para camada '%s'"), *LayerName));
            return Response;
        }
        
        const TArray<TSharedPtr<FJsonObject>>& Metrics = GameplayMetricsCache[LayerName];
        
        // Simular exportação
        TSharedPtr<FJsonObject> ExportInfo = MakeShared<FJsonObject>();
        ExportInfo->SetStringField(TEXT("format"), ExportFormat);
        ExportInfo->SetStringField(TEXT("file_path"), FilePath);
        ExportInfo->SetNumberField(TEXT("records_exported"), Metrics.Num());
        ExportInfo->SetStringField(TEXT("exported_at"), FDateTime::Now().ToString());
        ExportInfo->SetNumberField(TEXT("file_size_mb"), FMath::RandRange(1.0f, 50.0f));
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Dados exportados para '%s' no formato '%s'"), *FilePath, *ExportFormat));
        Response->SetObjectField(TEXT("export_info"), ExportInfo);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Dados exportados para '%s'"), *FilePath);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao exportar dados de analytics: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands::ExportAnalyticsData: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::ImportAnalyticsData(const FString& LayerName,
                                                                   const FString& FilePath)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Simular importação
        TSharedPtr<FJsonObject> ImportInfo = MakeShared<FJsonObject>();
        ImportInfo->SetStringField(TEXT("file_path"), FilePath);
        ImportInfo->SetNumberField(TEXT("records_imported"), FMath::RandRange(100, 1000));
        ImportInfo->SetStringField(TEXT("imported_at"), FDateTime::Now().ToString());
        ImportInfo->SetNumberField(TEXT("file_size_mb"), FMath::RandRange(5.0f, 100.0f));
        
        // Inicializar cache se não existir
        if (!GameplayMetricsCache.Contains(LayerName))
        {
            GameplayMetricsCache.Add(LayerName, TArray<TSharedPtr<FJsonObject>>());
        }
        
        // Simular adição de dados importados
        int32 RecordsToAdd = FMath::RandRange(10, 50);
        for (int32 i = 0; i < RecordsToAdd; ++i)
        {
            TSharedPtr<FJsonObject> ImportedMetric = MakeShared<FJsonObject>();
            ImportedMetric->SetStringField(TEXT("event_type"), TEXT("imported_event"));
            ImportedMetric->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
            ImportedMetric->SetNumberField(TEXT("fps"), FMath::RandRange(30, 120));
            ImportedMetric->SetNumberField(TEXT("memory_usage_mb"), FMath::RandRange(200, 1000));
            GameplayMetricsCache[LayerName].Add(ImportedMetric);
        }
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Dados importados de '%s' para camada '%s'"), *FilePath, *LayerName));
        Response->SetObjectField(TEXT("import_info"), ImportInfo);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Dados importados de '%s'"), *FilePath);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao importar dados de analytics: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands::ImportAnalyticsData: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::GetPerformanceStatistics(const FString& LayerName,
                                                                        const FString& StatType)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> Statistics = MakeShared<FJsonObject>();
        Statistics->SetStringField(TEXT("layer_name"), LayerName);
        Statistics->SetStringField(TEXT("stat_type"), StatType);
        Statistics->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
        
        if (StatType == TEXT("fps"))
        {
            Statistics->SetNumberField(TEXT("current_fps"), FMath::RandRange(30, 120));
            Statistics->SetNumberField(TEXT("avg_fps"), FMath::RandRange(60, 90));
            Statistics->SetNumberField(TEXT("min_fps"), FMath::RandRange(20, 40));
            Statistics->SetNumberField(TEXT("max_fps"), FMath::RandRange(100, 144));
        }
        else if (StatType == TEXT("memory"))
        {
            Statistics->SetNumberField(TEXT("current_memory_mb"), FMath::RandRange(200, 1000));
            Statistics->SetNumberField(TEXT("peak_memory_mb"), FMath::RandRange(800, 1500));
            Statistics->SetNumberField(TEXT("avg_memory_mb"), FMath::RandRange(400, 700));
            Statistics->SetNumberField(TEXT("memory_growth_rate"), FMath::RandRange(0.1f, 2.0f));
        }
        else if (StatType == TEXT("network"))
        {
            Statistics->SetNumberField(TEXT("bandwidth_mbps"), FMath::RandRange(10, 100));
            Statistics->SetNumberField(TEXT("latency_ms"), FMath::RandRange(20, 150));
            Statistics->SetNumberField(TEXT("packet_loss_percent"), FMath::RandRange(0.0f, 5.0f));
            Statistics->SetNumberField(TEXT("active_connections"), FMath::RandRange(1, 50));
        }
        else if (StatType == TEXT("gameplay"))
        {
            Statistics->SetNumberField(TEXT("active_players"), FMath::RandRange(1, 100));
            Statistics->SetNumberField(TEXT("events_per_second"), FMath::RandRange(10, 500));
            Statistics->SetNumberField(TEXT("avg_session_duration_min"), FMath::RandRange(15, 120));
            Statistics->SetNumberField(TEXT("player_retention_rate"), FMath::RandRange(0.6f, 0.95f));
        }
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Estatísticas de '%s' obtidas para camada '%s'"), *StatType, *LayerName));
        Response->SetObjectField(TEXT("statistics"), Statistics);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Estatísticas de '%s' obtidas para camada '%s'"), *StatType, *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao obter estatísticas de performance"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SetupCustomTelemetry(const FString& LayerName,
                                                                    const TSharedPtr<FJsonObject>& TelemetryConfig)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Armazenar configuração de telemetria personalizada
        CustomTelemetryConfig.Add(LayerName, TelemetryConfig);
        
        TSharedPtr<FJsonObject> SetupInfo = MakeShared<FJsonObject>();
        SetupInfo->SetStringField(TEXT("layer_name"), LayerName);
        SetupInfo->SetStringField(TEXT("configured_at"), FDateTime::Now().ToString());
        SetupInfo->SetBoolField(TEXT("auto_collect"), TelemetryConfig->GetBoolField(TEXT("auto_collect")));
        SetupInfo->SetNumberField(TEXT("collection_interval_sec"), TelemetryConfig->GetNumberField(TEXT("collection_interval_sec")));
        SetupInfo->SetStringField(TEXT("data_retention_days"), TelemetryConfig->GetStringField(TEXT("data_retention_days")));
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Telemetria personalizada configurada para camada '%s'"), *LayerName));
        Response->SetObjectField(TEXT("setup_info"), SetupInfo);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Telemetria personalizada configurada para camada '%s'"), *LayerName);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao configurar telemetria personalizada: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands::SetupCustomTelemetry: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::GeneratePlayerBehaviorReport(const FString& LayerName,
                                                                            const FString& PlayerId,
                                                                            const FString& ReportType)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> BehaviorReport = MakeShared<FJsonObject>();
        BehaviorReport->SetStringField(TEXT("layer_name"), LayerName);
        BehaviorReport->SetStringField(TEXT("player_id"), PlayerId);
        BehaviorReport->SetStringField(TEXT("report_type"), ReportType);
        BehaviorReport->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
        
        if (ReportType == TEXT("movement"))
        {
            BehaviorReport->SetNumberField(TEXT("total_distance_traveled"), FMath::RandRange(1000.0f, 50000.0f));
            BehaviorReport->SetNumberField(TEXT("avg_speed"), FMath::RandRange(100.0f, 800.0f));
            BehaviorReport->SetNumberField(TEXT("time_stationary_percent"), FMath::RandRange(10.0f, 40.0f));
            BehaviorReport->SetNumberField(TEXT("preferred_movement_direction"), FMath::RandRange(0.0f, 360.0f));
        }
        else if (ReportType == TEXT("interaction"))
        {
            BehaviorReport->SetNumberField(TEXT("total_interactions"), FMath::RandRange(50, 500));
            BehaviorReport->SetNumberField(TEXT("avg_interaction_duration_sec"), FMath::RandRange(2.0f, 30.0f));
            BehaviorReport->SetStringField(TEXT("most_used_interaction"), TEXT("object_pickup"));
            BehaviorReport->SetNumberField(TEXT("interaction_success_rate"), FMath::RandRange(0.7f, 0.98f));
        }
        else if (ReportType == TEXT("combat"))
        {
            BehaviorReport->SetNumberField(TEXT("total_combat_encounters"), FMath::RandRange(10, 100));
            BehaviorReport->SetNumberField(TEXT("avg_combat_duration_sec"), FMath::RandRange(15.0f, 120.0f));
            BehaviorReport->SetNumberField(TEXT("win_rate"), FMath::RandRange(0.4f, 0.9f));
            BehaviorReport->SetStringField(TEXT("preferred_combat_style"), TEXT("aggressive"));
        }
        else if (ReportType == TEXT("exploration"))
        {
            BehaviorReport->SetNumberField(TEXT("areas_discovered"), FMath::RandRange(5, 50));
            BehaviorReport->SetNumberField(TEXT("exploration_efficiency"), FMath::RandRange(0.3f, 0.8f));
            BehaviorReport->SetNumberField(TEXT("time_spent_exploring_percent"), FMath::RandRange(20.0f, 70.0f));
            BehaviorReport->SetStringField(TEXT("exploration_pattern"), TEXT("systematic"));
        }
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Relatório de comportamento '%s' gerado para jogador '%s'"), *ReportType, *PlayerId));
        Response->SetObjectField(TEXT("behavior_report"), BehaviorReport);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Relatório de comportamento gerado para jogador '%s'"), *PlayerId);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao gerar relatório de comportamento do jogador: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands::GeneratePlayerBehaviorReport: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::CreateRealtimeDashboard(const FString& LayerName,
                                                                       const TArray<FString>& MetricTypes)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> Dashboard = MakeShared<FJsonObject>();
        Dashboard->SetStringField(TEXT("layer_name"), LayerName);
        Dashboard->SetStringField(TEXT("created_at"), FDateTime::Now().ToString());
        Dashboard->SetStringField(TEXT("dashboard_id"), FGuid::NewGuid().ToString());
        Dashboard->SetNumberField(TEXT("refresh_interval_sec"), 5);
        
        TArray<TSharedPtr<FJsonValue>> Widgets;
        for (const FString& MetricType : MetricTypes)
        {
            TSharedPtr<FJsonObject> Widget = MakeShared<FJsonObject>();
            Widget->SetStringField(TEXT("metric_type"), MetricType);
            Widget->SetStringField(TEXT("widget_type"), TEXT("line_chart"));
            Widget->SetNumberField(TEXT("position_x"), FMath::RandRange(0, 800));
            Widget->SetNumberField(TEXT("position_y"), FMath::RandRange(0, 600));
            Widget->SetNumberField(TEXT("width"), 300);
            Widget->SetNumberField(TEXT("height"), 200);
            
            if (MetricType == TEXT("fps"))
            {
                Widget->SetNumberField(TEXT("current_value"), FMath::RandRange(30, 120));
                Widget->SetStringField(TEXT("unit"), TEXT("frames/sec"));
            }
            else if (MetricType == TEXT("memory"))
            {
                Widget->SetNumberField(TEXT("current_value"), FMath::RandRange(200, 1000));
                Widget->SetStringField(TEXT("unit"), TEXT("MB"));
            }
            else if (MetricType == TEXT("players"))
            {
                Widget->SetNumberField(TEXT("current_value"), FMath::RandRange(1, 100));
                Widget->SetStringField(TEXT("unit"), TEXT("count"));
            }
            
            Widgets.Add(MakeShared<FJsonValueObject>(Widget));
        }
        
        Dashboard->SetArrayField(TEXT("widgets"), Widgets);
        
        // Armazenar dashboard em cache
        RealTimeDashboardData.Add(LayerName, Dashboard);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Dashboard em tempo real criado para camada '%s' com %d widgets"), *LayerName, MetricTypes.Num()));
        Response->SetObjectField(TEXT("dashboard"), Dashboard);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Dashboard em tempo real criado para camada '%s'"), *LayerName);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao criar dashboard em tempo real: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands: Erro ao criar dashboard: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::AggregateMultiLayerData(const TArray<FString>& LayerNames,
                                                                       const FString& AggregationType)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> AggregatedData = MakeShared<FJsonObject>();
        AggregatedData->SetStringField(TEXT("aggregation_type"), AggregationType);
        AggregatedData->SetStringField(TEXT("aggregated_at"), FDateTime::Now().ToString());
        AggregatedData->SetNumberField(TEXT("total_layers"), LayerNames.Num());
        
        TArray<TSharedPtr<FJsonValue>> LayerSummaries;
        float TotalFPS = 0.0f;
        float TotalMemory = 0.0f;
        int32 TotalPlayers = 0;
        
        for (const FString& LayerName : LayerNames)
        {
            TSharedPtr<FJsonObject> LayerSummary = MakeShared<FJsonObject>();
            LayerSummary->SetStringField(TEXT("layer_name"), LayerName);
            
            float LayerFPS = FMath::RandRange(30.0f, 120.0f);
            float LayerMemory = FMath::RandRange(200.0f, 1000.0f);
            int32 LayerPlayers = FMath::RandRange(1, 50);
            
            LayerSummary->SetNumberField(TEXT("avg_fps"), LayerFPS);
            LayerSummary->SetNumberField(TEXT("memory_usage_mb"), LayerMemory);
            LayerSummary->SetNumberField(TEXT("active_players"), LayerPlayers);
            LayerSummary->SetNumberField(TEXT("events_count"), FMath::RandRange(100, 1000));
            
            TotalFPS += LayerFPS;
            TotalMemory += LayerMemory;
            TotalPlayers += LayerPlayers;
            
            LayerSummaries.Add(MakeShared<FJsonValueObject>(LayerSummary));
        }
        
        AggregatedData->SetArrayField(TEXT("layer_summaries"), LayerSummaries);
        
        if (AggregationType == TEXT("average"))
        {
            AggregatedData->SetNumberField(TEXT("avg_fps_across_layers"), TotalFPS / LayerNames.Num());
            AggregatedData->SetNumberField(TEXT("avg_memory_across_layers"), TotalMemory / LayerNames.Num());
            AggregatedData->SetNumberField(TEXT("avg_players_per_layer"), float(TotalPlayers) / LayerNames.Num());
        }
        else if (AggregationType == TEXT("sum"))
        {
            AggregatedData->SetNumberField(TEXT("total_fps"), TotalFPS);
            AggregatedData->SetNumberField(TEXT("total_memory_mb"), TotalMemory);
            AggregatedData->SetNumberField(TEXT("total_players"), TotalPlayers);
        }
        
        // Armazenar dados agregados
        MultiLayerAggregationConfig->SetObjectField(FString::Join(LayerNames, TEXT(",")), AggregatedData);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Dados agregados de %d camadas usando método '%s'"), LayerNames.Num(), *AggregationType));
        Response->SetObjectField(TEXT("aggregated_data"), AggregatedData);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Dados agregados de %d camadas"), LayerNames.Num());
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao agregar dados multicamada: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands: Erro ao agregar dados: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::RunPredictiveAnalysis(const FString& LayerName,
                                                                     const FString& PredictionType,
                                                                     int32 ForecastDays)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> PredictiveAnalysis = MakeShared<FJsonObject>();
        PredictiveAnalysis->SetStringField(TEXT("layer_name"), LayerName);
        PredictiveAnalysis->SetStringField(TEXT("prediction_type"), PredictionType);
        PredictiveAnalysis->SetNumberField(TEXT("forecast_days"), ForecastDays);
        PredictiveAnalysis->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
        PredictiveAnalysis->SetStringField(TEXT("model_version"), TEXT("v2.1.0"));
        PredictiveAnalysis->SetNumberField(TEXT("confidence_score"), FMath::RandRange(0.7f, 0.95f));
        
        TArray<TSharedPtr<FJsonValue>> Predictions;
        for (int32 Day = 1; Day <= ForecastDays; ++Day)
        {
            TSharedPtr<FJsonObject> DayPrediction = MakeShared<FJsonObject>();
            DayPrediction->SetNumberField(TEXT("day"), Day);
            DayPrediction->SetStringField(TEXT("date"), FDateTime::Now().ToString());
            
            if (PredictionType == TEXT("player_count"))
            {
                DayPrediction->SetNumberField(TEXT("predicted_players"), FMath::RandRange(50, 200));
                DayPrediction->SetNumberField(TEXT("confidence_interval_low"), FMath::RandRange(40, 60));
                DayPrediction->SetNumberField(TEXT("confidence_interval_high"), FMath::RandRange(180, 220));
            }
            else if (PredictionType == TEXT("performance"))
            {
                DayPrediction->SetNumberField(TEXT("predicted_avg_fps"), FMath::RandRange(60, 90));
                DayPrediction->SetNumberField(TEXT("predicted_memory_usage_mb"), FMath::RandRange(400, 800));
                DayPrediction->SetNumberField(TEXT("predicted_load_factor"), FMath::RandRange(0.3f, 0.8f));
            }
            else if (PredictionType == TEXT("engagement"))
            {
                DayPrediction->SetNumberField(TEXT("predicted_session_duration_min"), FMath::RandRange(30, 120));
                DayPrediction->SetNumberField(TEXT("predicted_retention_rate"), FMath::RandRange(0.6f, 0.9f));
                DayPrediction->SetNumberField(TEXT("predicted_activity_score"), FMath::RandRange(0.4f, 0.9f));
            }
            
            Predictions.Add(MakeShared<FJsonValueObject>(DayPrediction));
        }
        
        PredictiveAnalysis->SetArrayField(TEXT("predictions"), Predictions);
        
        // Armazenar análise preditiva
        PredictiveAnalyticsCache.Add(LayerName, PredictiveAnalysis);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Análise preditiva '%s' executada para camada '%s' com previsão de %d dias"), *PredictionType, *LayerName, ForecastDays));
        Response->SetObjectField(TEXT("predictive_analysis"), PredictiveAnalysis);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Análise preditiva executada para camada '%s'"), *LayerName);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao executar análise preditiva: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands: Erro na análise preditiva: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SetupABTesting(const FString& LayerName,
                                                              const TSharedPtr<FJsonObject>& TestConfig)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Armazenar configuração de A/B testing
        ABTestingConfigs.Add(LayerName, TestConfig);
        
        TSharedPtr<FJsonObject> TestSetup = MakeShared<FJsonObject>();
        TestSetup->SetStringField(TEXT("layer_name"), LayerName);
        TestSetup->SetStringField(TEXT("test_name"), TestConfig->GetStringField(TEXT("test_name")));
        TestSetup->SetStringField(TEXT("test_type"), TestConfig->GetStringField(TEXT("test_type")));
        TestSetup->SetStringField(TEXT("started_at"), FDateTime::Now().ToString());
        TestSetup->SetNumberField(TEXT("duration_days"), TestConfig->GetNumberField(TEXT("duration_days")));
        TestSetup->SetNumberField(TEXT("traffic_split_percent"), TestConfig->GetNumberField(TEXT("traffic_split_percent")));
        TestSetup->SetStringField(TEXT("test_id"), FGuid::NewGuid().ToString());
        
        // Simular grupos de teste
        TArray<TSharedPtr<FJsonValue>> TestGroups;
        TSharedPtr<FJsonObject> GroupA = MakeShared<FJsonObject>();
        GroupA->SetStringField(TEXT("group_name"), TEXT("Control"));
        GroupA->SetNumberField(TEXT("allocation_percent"), 50.0f);
        GroupA->SetNumberField(TEXT("current_participants"), FMath::RandRange(20, 100));
        TestGroups.Add(MakeShared<FJsonValueObject>(GroupA));
        
        TSharedPtr<FJsonObject> GroupB = MakeShared<FJsonObject>();
        GroupB->SetStringField(TEXT("group_name"), TEXT("Variant"));
        GroupB->SetNumberField(TEXT("allocation_percent"), 50.0f);
        GroupB->SetNumberField(TEXT("current_participants"), FMath::RandRange(20, 100));
        TestGroups.Add(MakeShared<FJsonValueObject>(GroupB));
        
        TestSetup->SetArrayField(TEXT("test_groups"), TestGroups);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("A/B Testing configurado para camada '%s'"), *LayerName));
        Response->SetObjectField(TEXT("test_setup"), TestSetup);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: A/B Testing configurado para camada '%s'"), *LayerName);
    }
    catch (const std::exception& e)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Erro ao configurar A/B Testing: %s"), UTF8_TO_TCHAR(e.what())));
        UE_LOG(LogTemp, Error, TEXT("FUnrealMCPRealmCommands: Erro no A/B Testing: %s"), UTF8_TO_TCHAR(e.what()));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::GenerateHeatmapAnalysis(const FString& LayerName,
                                                                       const FString& HeatmapType,
                                                                       const FVector& WorldBounds)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        TSharedPtr<FJsonObject> HeatmapAnalysis = MakeShared<FJsonObject>();
        HeatmapAnalysis->SetStringField(TEXT("layer_name"), LayerName);
        HeatmapAnalysis->SetStringField(TEXT("heatmap_type"), HeatmapType);
        HeatmapAnalysis->SetStringField(TEXT("generated_at"), FDateTime::Now().ToString());
        HeatmapAnalysis->SetStringField(TEXT("world_bounds"), FString::Printf(TEXT("%.2f,%.2f,%.2f"), WorldBounds.X, WorldBounds.Y, WorldBounds.Z));
        
        // Simular dados de heatmap
        TArray<TSharedPtr<FJsonValue>> HeatmapData;
        int32 GridSize = 20; // Grid 20x20
        
        for (int32 X = 0; X < GridSize; ++X)
        {
            for (int32 Y = 0; Y < GridSize; ++Y)
            {
                TSharedPtr<FJsonObject> DataPoint = MakeShared<FJsonObject>();
                DataPoint->SetNumberField(TEXT("grid_x"), X);
                DataPoint->SetNumberField(TEXT("grid_y"), Y);
                DataPoint->SetNumberField(TEXT("world_x"), (WorldBounds.X / GridSize) * X);
                DataPoint->SetNumberField(TEXT("world_y"), (WorldBounds.Y / GridSize) * Y);
                
                if (HeatmapType == TEXT("player_density"))
                {
                    DataPoint->SetNumberField(TEXT("intensity"), FMath::RandRange(0.0f, 1.0f));
                    DataPoint->SetNumberField(TEXT("player_count"), FMath::RandRange(0, 20));
                    DataPoint->SetNumberField(TEXT("time_spent_sec"), FMath::RandRange(0, 300));
                }
                else if (HeatmapType == TEXT("interaction_hotspots"))
                {
                    DataPoint->SetNumberField(TEXT("intensity"), FMath::RandRange(0.0f, 1.0f));
                    DataPoint->SetNumberField(TEXT("interaction_count"), FMath::RandRange(0, 50));
                    DataPoint->SetStringField(TEXT("most_common_interaction"), TEXT("object_pickup"));
                }
                else if (HeatmapType == TEXT("performance_impact"))
                {
                    DataPoint->SetNumberField(TEXT("intensity"), FMath::RandRange(0.0f, 1.0f));
                    DataPoint->SetNumberField(TEXT("avg_fps_drop"), FMath::RandRange(0.0f, 30.0f));
                    DataPoint->SetNumberField(TEXT("memory_spike_mb"), FMath::RandRange(0.0f, 200.0f));
                }
                
                HeatmapData.Add(MakeShared<FJsonValueObject>(DataPoint));
            }
        }
        
        HeatmapAnalysis->SetArrayField(TEXT("heatmap_data"), HeatmapData);
        HeatmapAnalysis->SetNumberField(TEXT("grid_resolution"), GridSize);
        HeatmapAnalysis->SetNumberField(TEXT("total_data_points"), HeatmapData.Num());
        
        // Armazenar dados de heatmap
        HeatmapDataCache.Add(LayerName, HeatmapAnalysis);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Análise de heatmap '%s' gerada para camada '%s' com %d pontos de dados"), *HeatmapType, *LayerName, HeatmapData.Num()));
        Response->SetObjectField(TEXT("heatmap_analysis"), HeatmapAnalysis);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Análise de heatmap gerada para camada '%s'"), *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao gerar análise de heatmap"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::CreateSuccessResponse(const TSharedPtr<FJsonObject>& Data, const FString& Message)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
    Response->SetStringField(TEXT("message"), Message);
    Response->SetObjectField(TEXT("data"), Data);
    Response->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulateRealmSystemCreation(const FString& LayerName, 
                                                                           const TArray<TSharedPtr<FJsonValue>>& RealmConfigs,
                                                                           const TArray<TSharedPtr<FJsonValue>>& TransitionConfigs)
{
    TSharedPtr<FJsonObject> SystemData = MakeShared<FJsonObject>();
    
    // Informações básicas do sistema
    SystemData->SetStringField(TEXT("layer_name"), LayerName);
    SystemData->SetStringField(TEXT("system_id"), FGuid::NewGuid().ToString());
    SystemData->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    SystemData->SetNumberField(TEXT("realm_count"), RealmConfigs.Num());
    SystemData->SetNumberField(TEXT("transition_count"), TransitionConfigs.Num());
    
    // Configurações de realms processadas
    TArray<TSharedPtr<FJsonValue>> ProcessedRealms;
    for (int32 i = 0; i < RealmConfigs.Num(); i++)
    {
        TSharedPtr<FJsonObject> RealmData = MakeShared<FJsonObject>();
        RealmData->SetStringField(TEXT("realm_id"), FString::Printf(TEXT("%s_realm_%d"), *LayerName, i));
        RealmData->SetStringField(TEXT("status"), TEXT("created"));
        RealmData->SetNumberField(TEXT("memory_allocated_mb"), FMath::RandRange(50, 200));
        ProcessedRealms.Add(MakeShared<FJsonValueObject>(RealmData));
    }
    SystemData->SetArrayField(TEXT("realms"), ProcessedRealms);
    
    // Configurações de transições processadas
    TArray<TSharedPtr<FJsonValue>> ProcessedTransitions;
    for (int32 i = 0; i < TransitionConfigs.Num(); i++)
    {
        TSharedPtr<FJsonObject> TransitionData = MakeShared<FJsonObject>();
        TransitionData->SetStringField(TEXT("transition_id"), FString::Printf(TEXT("%s_transition_%d"), *LayerName, i));
        TransitionData->SetStringField(TEXT("status"), TEXT("configured"));
        TransitionData->SetNumberField(TEXT("estimated_time_ms"), FMath::RandRange(100, 2000));
        ProcessedTransitions.Add(MakeShared<FJsonValueObject>(TransitionData));
    }
    SystemData->SetArrayField(TEXT("transitions"), ProcessedTransitions);
    
    // Métricas estimadas
    TSharedPtr<FJsonObject> EstimatedMetrics = MakeShared<FJsonObject>();
    EstimatedMetrics->SetNumberField(TEXT("total_memory_mb"), RealmConfigs.Num() * 125);
    EstimatedMetrics->SetNumberField(TEXT("average_transition_time_ms"), 750);
    EstimatedMetrics->SetNumberField(TEXT("concurrent_realms_supported"), FMath::Min(RealmConfigs.Num(), 8));
    SystemData->SetObjectField(TEXT("estimated_metrics"), EstimatedMetrics);
    
    return SystemData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulateStreamingConfiguration(const FString& LayerName,
                                                                              const FString& Strategy,
                                                                              const TSharedPtr<FJsonObject>& AssetPriorities,
                                                                              const TSharedPtr<FJsonObject>& BandwidthLimits)
{
    TSharedPtr<FJsonObject> StreamingData = MakeShared<FJsonObject>();
    
    // Informações básicas
    StreamingData->SetStringField(TEXT("layer_name"), LayerName);
    StreamingData->SetStringField(TEXT("strategy"), Strategy);
    StreamingData->SetStringField(TEXT("configuration_time"), FDateTime::Now().ToString());
    
    // Configuração de prioridades
    StreamingData->SetObjectField(TEXT("asset_priorities"), AssetPriorities);
    StreamingData->SetObjectField(TEXT("bandwidth_limits"), BandwidthLimits);
    
    // Métricas de streaming simuladas
    TSharedPtr<FJsonObject> StreamingMetrics = MakeShared<FJsonObject>();
    StreamingMetrics->SetNumberField(TEXT("preload_time_ms"), FMath::RandRange(500, 3000));
    StreamingMetrics->SetNumberField(TEXT("bandwidth_usage_mbps"), FMath::RandRange(10, 100));
    StreamingMetrics->SetNumberField(TEXT("cache_efficiency"), FMath::RandRange(0.7, 0.95));
    StreamingMetrics->SetNumberField(TEXT("assets_cached"), FMath::RandRange(50, 500));
    StreamingData->SetObjectField(TEXT("metrics"), StreamingMetrics);
    
    // Configurações otimizadas
    TSharedPtr<FJsonObject> OptimizedSettings = MakeShared<FJsonObject>();
    OptimizedSettings->SetNumberField(TEXT("chunk_size_kb"), 1024);
    OptimizedSettings->SetNumberField(TEXT("max_concurrent_downloads"), 4);
    OptimizedSettings->SetBoolField(TEXT("compression_enabled"), true);
    OptimizedSettings->SetStringField(TEXT("cache_policy"), TEXT("lru"));
    StreamingData->SetObjectField(TEXT("optimized_settings"), OptimizedSettings);
    
    return StreamingData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulatePartitioningConfiguration(const FString& LayerName,
                                                                                 const FString& Method,
                                                                                 const TArray<TSharedPtr<FJsonValue>>& PartitionSize,
                                                                                 float OverlapDistance)
{
    TSharedPtr<FJsonObject> PartitioningData = MakeShared<FJsonObject>();
    
    // Informações básicas
    PartitioningData->SetStringField(TEXT("layer_name"), LayerName);
    PartitioningData->SetStringField(TEXT("method"), Method);
    PartitioningData->SetArrayField(TEXT("partition_size"), PartitionSize);
    PartitioningData->SetNumberField(TEXT("overlap_distance"), OverlapDistance);
    PartitioningData->SetStringField(TEXT("configuration_time"), FDateTime::Now().ToString());
    
    // Estatísticas de particionamento simuladas
    TSharedPtr<FJsonObject> PartitionStats = MakeShared<FJsonObject>();
    PartitionStats->SetNumberField(TEXT("total_partitions"), FMath::RandRange(16, 256));
    PartitionStats->SetNumberField(TEXT("active_partitions"), FMath::RandRange(4, 16));
    PartitionStats->SetNumberField(TEXT("memory_per_partition_mb"), FMath::RandRange(25, 100));
    PartitionStats->SetNumberField(TEXT("load_balance_efficiency"), FMath::RandRange(0.8, 0.98));
    PartitioningData->SetObjectField(TEXT("statistics"), PartitionStats);
    
    // Configurações de otimização
    TSharedPtr<FJsonObject> OptimizationConfig = MakeShared<FJsonObject>();
    OptimizationConfig->SetBoolField(TEXT("dynamic_loading"), true);
    OptimizationConfig->SetBoolField(TEXT("predictive_caching"), true);
    OptimizationConfig->SetNumberField(TEXT("unload_delay_seconds"), 30);
    OptimizationConfig->SetStringField(TEXT("priority_algorithm"), TEXT("distance_weighted"));
    PartitioningData->SetObjectField(TEXT("optimization"), OptimizationConfig);
    
    return PartitioningData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulateTriggerCreation(const FString& LayerName,
                                                                       const TArray<TSharedPtr<FJsonValue>>& TriggerConfigs)
{
    TSharedPtr<FJsonObject> TriggerData = MakeShared<FJsonObject>();
    
    // Get current world
    UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get world for trigger creation"));
        TriggerData->SetStringField(TEXT("status"), TEXT("error"));
        TriggerData->SetStringField(TEXT("error"), TEXT("World not available"));
        return TriggerData;
    }
    
    // Basic information
    TriggerData->SetStringField(TEXT("layer_name"), LayerName);
    TriggerData->SetNumberField(TEXT("trigger_count"), TriggerConfigs.Num());
    TriggerData->SetStringField(TEXT("creation_time"), FDateTime::Now().ToString());
    
    // Create actual trigger volumes
    TArray<TSharedPtr<FJsonValue>> CreatedTriggers;
    for (int32 i = 0; i < TriggerConfigs.Num(); i++)
    {
        const TSharedPtr<FJsonObject>* ConfigObject;
        if (TriggerConfigs[i]->TryGetObject(ConfigObject) && ConfigObject->IsValid())
        {
            // Extract trigger configuration
            FVector Location = FVector::ZeroVector;
            FVector Scale = FVector(1.0f, 1.0f, 1.0f);
            FString TriggerType = TEXT("box");
            
            const TArray<TSharedPtr<FJsonValue>>* LocationArray;
            if ((*ConfigObject)->TryGetArrayField(TEXT("location"), LocationArray) && LocationArray->Num() >= 3)
            {
                Location.X = (*LocationArray)[0]->AsNumber();
                Location.Y = (*LocationArray)[1]->AsNumber();
                Location.Z = (*LocationArray)[2]->AsNumber();
            }
            
            const TArray<TSharedPtr<FJsonValue>>* ScaleArray;
            if ((*ConfigObject)->TryGetArrayField(TEXT("scale"), ScaleArray) && ScaleArray->Num() >= 3)
            {
                Scale.X = (*ScaleArray)[0]->AsNumber();
                Scale.Y = (*ScaleArray)[1]->AsNumber();
                Scale.Z = (*ScaleArray)[2]->AsNumber();
            }
            
            (*ConfigObject)->TryGetStringField(TEXT("type"), TriggerType);
            
            // Create trigger volume actor
            FString TriggerName = FString::Printf(TEXT("%s_trigger_%d"), *LayerName, i);
            ATriggerVolume* TriggerVolume = World->SpawnActor<ATriggerVolume>(ATriggerVolume::StaticClass(), Location, FRotator::ZeroRotator);
            
            if (TriggerVolume)
            {
                // Set trigger properties
                TriggerVolume->SetActorLabel(TriggerName);
                TriggerVolume->SetActorScale3D(Scale);
                
                // Configure collision
                UBrushComponent* BrushComponent = TriggerVolume->GetBrushComponent();
                if (BrushComponent)
                {
                    BrushComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
                    BrushComponent->SetCollisionResponseToAllChannels(ECR_Ignore);
                    BrushComponent->SetCollisionResponseToChannel(ECC_Pawn, ECR_Overlap);
                     BrushComponent->SetGenerateOverlapEvents(true);
                }
                
                // Store trigger information
                TSharedPtr<FJsonObject> TriggerInfo = MakeShared<FJsonObject>();
                TriggerInfo->SetStringField(TEXT("trigger_id"), TriggerName);
                TriggerInfo->SetStringField(TEXT("type"), TriggerType);
                TriggerInfo->SetStringField(TEXT("status"), TEXT("active"));
                TriggerInfo->SetNumberField(TEXT("activation_count"), 0);
                TriggerInfo->SetStringField(TEXT("actor_name"), TriggerVolume->GetName());
                
                // Add location and scale info
                TArray<TSharedPtr<FJsonValue>> LocationJson;
                LocationJson.Add(MakeShared<FJsonValueNumber>(Location.X));
                LocationJson.Add(MakeShared<FJsonValueNumber>(Location.Y));
                LocationJson.Add(MakeShared<FJsonValueNumber>(Location.Z));
                TriggerInfo->SetArrayField(TEXT("location"), LocationJson);
                
                TArray<TSharedPtr<FJsonValue>> ScaleJson;
                ScaleJson.Add(MakeShared<FJsonValueNumber>(Scale.X));
                ScaleJson.Add(MakeShared<FJsonValueNumber>(Scale.Y));
                ScaleJson.Add(MakeShared<FJsonValueNumber>(Scale.Z));
                TriggerInfo->SetArrayField(TEXT("scale"), ScaleJson);
                
                CreatedTriggers.Add(MakeShared<FJsonValueObject>(TriggerInfo));
                
                UE_LOG(LogTemp, Log, TEXT("Created trigger volume: %s at location %s"), *TriggerName, *Location.ToString());
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to create trigger volume %d for layer %s"), i, *LayerName);
            }
        }
    }
    
    TriggerData->SetArrayField(TEXT("triggers"), CreatedTriggers);
    
    // Monitoring configuration
    TSharedPtr<FJsonObject> MonitoringConfig = MakeShared<FJsonObject>();
    MonitoringConfig->SetBoolField(TEXT("collision_detection"), true);
    MonitoringConfig->SetBoolField(TEXT("performance_tracking"), true);
    MonitoringConfig->SetNumberField(TEXT("check_frequency_hz"), 60);
    TriggerData->SetObjectField(TEXT("monitoring"), MonitoringConfig);
    
    TriggerData->SetStringField(TEXT("status"), TEXT("success"));
    
    return TriggerData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulatePersistenceConfiguration(const FString& LayerName,
                                                                                const TSharedPtr<FJsonObject>& PersistenceSettings)
{
    TSharedPtr<FJsonObject> PersistenceData = MakeShared<FJsonObject>();
    
    // Basic information
    PersistenceData->SetStringField(TEXT("layer_name"), LayerName);
    PersistenceData->SetObjectField(TEXT("settings"), PersistenceSettings);
    PersistenceData->SetStringField(TEXT("configuration_time"), FDateTime::Now().ToString());
    
    // Extract persistence settings
    FString StorageType = TEXT("file_system");
    FString CompressionType = TEXT("lz4");
    bool bEncryptionEnabled = true;
    int32 AutoSaveInterval = 300;
    FString SaveSlotName = FString::Printf(TEXT("%s_realm_data"), *LayerName);
    
    if (PersistenceSettings.IsValid())
    {
        PersistenceSettings->TryGetStringField(TEXT("storage_type"), StorageType);
        PersistenceSettings->TryGetStringField(TEXT("compression"), CompressionType);
        PersistenceSettings->TryGetBoolField(TEXT("encryption_enabled"), bEncryptionEnabled);
        PersistenceSettings->TryGetNumberField(TEXT("auto_save_interval_seconds"), AutoSaveInterval);
        PersistenceSettings->TryGetStringField(TEXT("save_slot_name"), SaveSlotName);
    }
    
    // Configure save game system
    double SaveStartTime = FPlatformTime::Seconds();
    
    // Check if save game exists
    bool bSaveGameExists = UGameplayStatics::DoesSaveGameExist(SaveSlotName, 0);
    
    // Create or load save game object
    USaveGame* SaveGameObject = nullptr;
    if (bSaveGameExists)
    {
        SaveGameObject = UGameplayStatics::LoadGameFromSlot(SaveSlotName, 0);
        UE_LOG(LogTemp, Log, TEXT("Loaded existing save game for layer: %s"), *LayerName);
    }
    else
    {
        SaveGameObject = UGameplayStatics::CreateSaveGameObject(USaveGame::StaticClass());
        UE_LOG(LogTemp, Log, TEXT("Created new save game for layer: %s"), *LayerName);
    }
    
    double SaveEndTime = FPlatformTime::Seconds();
    double SaveTimeMs = (SaveEndTime - SaveStartTime) * 1000.0;
    
    // Configure storage settings
    TSharedPtr<FJsonObject> StorageConfig = MakeShared<FJsonObject>();
    StorageConfig->SetStringField(TEXT("storage_type"), StorageType);
    StorageConfig->SetStringField(TEXT("compression"), CompressionType);
    StorageConfig->SetBoolField(TEXT("encryption_enabled"), bEncryptionEnabled);
    StorageConfig->SetNumberField(TEXT("auto_save_interval_seconds"), AutoSaveInterval);
    StorageConfig->SetStringField(TEXT("save_slot_name"), SaveSlotName);
    StorageConfig->SetBoolField(TEXT("save_game_exists"), bSaveGameExists);
    PersistenceData->SetObjectField(TEXT("storage"), StorageConfig);
    
    // Get file system information
    FString SaveGamePath = FPaths::ProjectSavedDir() / TEXT("SaveGames") / (SaveSlotName + TEXT(".sav"));
    int64 FileSize = 0;
    if (FPaths::FileExists(SaveGamePath))
    {
        FileSize = IFileManager::Get().FileSize(*SaveGamePath);
    }
    
    // Calculate persistence statistics
    TSharedPtr<FJsonObject> PersistenceStats = MakeShared<FJsonObject>();
    PersistenceStats->SetNumberField(TEXT("save_time_ms"), SaveTimeMs);
    PersistenceStats->SetNumberField(TEXT("load_time_ms"), SaveTimeMs); // Same operation time for simplicity
    PersistenceStats->SetNumberField(TEXT("data_size_bytes"), FileSize);
    PersistenceStats->SetNumberField(TEXT("data_size_mb"), FileSize / (1024.0 * 1024.0));
    PersistenceStats->SetStringField(TEXT("save_game_path"), SaveGamePath);
    
    // Estimate compression ratio based on file type
    double CompressionRatio = 0.5; // Default estimate
    if (CompressionType == TEXT("lz4"))
    {
        CompressionRatio = 0.4;
    }
    else if (CompressionType == TEXT("zlib"))
    {
        CompressionRatio = 0.3;
    }
    PersistenceStats->SetNumberField(TEXT("compression_ratio"), CompressionRatio);
    
    PersistenceData->SetObjectField(TEXT("statistics"), PersistenceStats);
    
    // Set up auto-save timer if requested
    if (AutoSaveInterval > 0)
    {
        UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
        if (World)
        {
            FTimerHandle AutoSaveTimer;
            World->GetTimerManager().SetTimer(AutoSaveTimer, [SaveSlotName, LayerName]()
            {
                // Auto-save logic
                USaveGame* AutoSaveObject = UGameplayStatics::CreateSaveGameObject(USaveGame::StaticClass());
                if (AutoSaveObject)
                {
                    bool bSaveSuccess = UGameplayStatics::SaveGameToSlot(AutoSaveObject, SaveSlotName, 0);
                    UE_LOG(LogTemp, Log, TEXT("Auto-save for layer %s: %s"), *LayerName, bSaveSuccess ? TEXT("Success") : TEXT("Failed"));
                }
            }, AutoSaveInterval, true);
            
            UE_LOG(LogTemp, Log, TEXT("Auto-save timer configured for layer %s with interval %d seconds"), *LayerName, AutoSaveInterval);
        }
    }
    
    PersistenceData->SetStringField(TEXT("status"), TEXT("success"));
    
    UE_LOG(LogTemp, Log, TEXT("Persistence configuration completed for layer: %s"), *LayerName);
    
    return PersistenceData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::SimulateCommunicationConfiguration(const FString& LayerName,
                                                                                  const TArray<TSharedPtr<FJsonValue>>& Channels)
{
    TSharedPtr<FJsonObject> CommunicationData = MakeShared<FJsonObject>();
    
    // Get current world and network subsystem
    UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
    if (!World && GEngine)
    {
        // Try to get the first valid world from the engine
        for (const FWorldContext& Context : GEngine->GetWorldContexts())
        {
            if (Context.World())
            {
                World = Context.World();
                break;
            }
        }
    }
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to get world for communication configuration"));
        CommunicationData->SetStringField(TEXT("error"), TEXT("World not available"));
        return CommunicationData;
    }
    
    UNetDriver* NetDriver = World->GetNetDriver();
    UGameInstance* GameInstance = World->GetGameInstance();
    
    // Basic information
    CommunicationData->SetStringField(TEXT("layer_name"), LayerName);
    CommunicationData->SetNumberField(TEXT("channel_count"), Channels.Num());
    CommunicationData->SetStringField(TEXT("configuration_time"), FDateTime::Now().ToString());
    
    // Configure network channels
    TArray<TSharedPtr<FJsonValue>> ConfiguredChannels;
    for (int32 i = 0; i < Channels.Num(); i++)
    {
        TSharedPtr<FJsonObject> ChannelConfig = Channels[i]->AsObject();
        if (!ChannelConfig.IsValid()) continue;
        
        FString ChannelType = ChannelConfig->GetStringField(TEXT("type"));
        FString Protocol = ChannelConfig->GetStringField(TEXT("protocol"));
        int32 Priority = ChannelConfig->GetIntegerField(TEXT("priority"));
        
        TSharedPtr<FJsonObject> ChannelInfo = MakeShared<FJsonObject>();
        ChannelInfo->SetStringField(TEXT("channel_id"), FString::Printf(TEXT("%s_channel_%d"), *LayerName, i));
        ChannelInfo->SetStringField(TEXT("type"), ChannelType);
        ChannelInfo->SetStringField(TEXT("protocol"), Protocol.IsEmpty() ? TEXT("tcp") : Protocol);
        ChannelInfo->SetNumberField(TEXT("priority"), Priority);
        
        // Get actual network status if NetDriver is available
        if (NetDriver)
        {
            // Check if NetDriver is valid and has active connections
            bool bIsConnected = NetDriver->IsNetResourceValid() && NetDriver->ClientConnections.Num() > 0;
            ChannelInfo->SetStringField(TEXT("status"), bIsConnected ? TEXT("connected") : TEXT("disconnected"));
            
            // Calculate average latency from active connections
            double AverageLatency = 0.0;
            double TotalPacketLoss = 0.0;
            int32 ValidConnections = 0;
            
            for (UNetConnection* Connection : NetDriver->ClientConnections)
             {
                 if (Connection && Connection->IsNetReady())
                 {
                     AverageLatency += Connection->AvgLag * 1000.0; // Convert to milliseconds
                     TotalPacketLoss += static_cast<double>(Connection->OutPacketsLost) / FMath::Max(1, Connection->OutPackets) * 100.0;
                     ValidConnections++;
                 }
             }
            
            if (ValidConnections > 0)
            {
                AverageLatency /= ValidConnections;
                TotalPacketLoss /= ValidConnections;
            }
            
            ChannelInfo->SetNumberField(TEXT("latency_ms"), AverageLatency);
            ChannelInfo->SetNumberField(TEXT("packet_loss"), TotalPacketLoss);
        }
        else
        {
            ChannelInfo->SetStringField(TEXT("status"), TEXT("not_initialized"));
            ChannelInfo->SetNumberField(TEXT("latency_ms"), 0.0);
            ChannelInfo->SetNumberField(TEXT("packet_loss"), 0.0);
        }
        
        ConfiguredChannels.Add(MakeShared<FJsonValueObject>(ChannelInfo));
    }
    CommunicationData->SetArrayField(TEXT("channels"), ConfiguredChannels);
    
    // Network configuration from NetDriver
    TSharedPtr<FJsonObject> NetworkConfig = MakeShared<FJsonObject>();
    if (NetDriver)
    {
        NetworkConfig->SetNumberField(TEXT("max_client_rate"), static_cast<double>(NetDriver->MaxClientRate));
        NetworkConfig->SetNumberField(TEXT("timeout_seconds"), static_cast<double>(NetDriver->ConnectionTimeout));
        NetworkConfig->SetBoolField(TEXT("encryption_enabled"), false); // Default value since IsEncryptionEnabled() is not available
        NetworkConfig->SetStringField(TEXT("net_driver_name"), NetDriver->GetName());
        NetworkConfig->SetStringField(TEXT("net_driver_class"), NetDriver->GetClass()->GetName());
        NetworkConfig->SetNumberField(TEXT("active_connections"), static_cast<double>(NetDriver->ClientConnections.Num()));
    }
    else
    {
        NetworkConfig->SetNumberField(TEXT("max_client_rate"), 10000.0);
        NetworkConfig->SetNumberField(TEXT("timeout_seconds"), 30.0);
        NetworkConfig->SetBoolField(TEXT("encryption_enabled"), false);
        NetworkConfig->SetStringField(TEXT("net_driver_name"), TEXT("None"));
        NetworkConfig->SetStringField(TEXT("net_driver_class"), TEXT("None"));
        NetworkConfig->SetNumberField(TEXT("active_connections"), 0.0);
    }
    
    // Game instance network settings
    if (GameInstance)
    {
        NetworkConfig->SetStringField(TEXT("game_instance_class"), GameInstance->GetClass()->GetName());
        NetworkConfig->SetBoolField(TEXT("is_dedicated_server"), GameInstance->IsDedicatedServerInstance());
    }
    
    NetworkConfig->SetStringField(TEXT("protocol_version"), TEXT("1.0"));
    CommunicationData->SetObjectField(TEXT("network"), NetworkConfig);
    
    // World network information
    TSharedPtr<FJsonObject> WorldNetInfo = MakeShared<FJsonObject>();
    WorldNetInfo->SetStringField(TEXT("world_name"), World->GetName());
    WorldNetInfo->SetBoolField(TEXT("is_server"), World->GetNetMode() != NM_Client);
    WorldNetInfo->SetBoolField(TEXT("is_client"), World->GetNetMode() == NM_Client);
    WorldNetInfo->SetNumberField(TEXT("net_mode"), static_cast<int32>(World->GetNetMode()));
    CommunicationData->SetObjectField(TEXT("world_info"), WorldNetInfo);
    
    UE_LOG(LogTemp, Log, TEXT("Communication configuration completed for layer: %s with %d channels"), *LayerName, Channels.Num());
    
    return CommunicationData;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::CreateBasicPerformanceOptimization(const FString& LayerName,
                                                                                    const TSharedPtr<FJsonObject>& OptimizationSettings)
{
    TSharedPtr<FJsonObject> OptimizationData = MakeShared<FJsonObject>();
    
    // Get current world and performance subsystems
    UWorld* World = GEngine ? GEngine->GetWorldFromContextObject(GEngine, EGetWorldErrorMode::LogAndReturnNull) : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to get world for performance optimization"));
        OptimizationData->SetStringField(TEXT("error"), TEXT("World not available"));
        return OptimizationData;
    }
    
    // Basic information
    OptimizationData->SetStringField(TEXT("layer_name"), LayerName);
    OptimizationData->SetObjectField(TEXT("settings"), OptimizationSettings);
    OptimizationData->SetStringField(TEXT("optimization_time"), FDateTime::Now().ToString());
    
    // Extract optimization settings
    bool bEnableLOD = OptimizationSettings->GetBoolField(TEXT("enable_lod"));
    bool bEnableOcclusionCulling = OptimizationSettings->GetBoolField(TEXT("enable_occlusion_culling"));
    bool bEnableTextureStreaming = OptimizationSettings->GetBoolField(TEXT("enable_texture_streaming"));
    float MaxDrawDistance = OptimizationSettings->GetNumberField(TEXT("max_draw_distance"));
    int32 TargetFPS = OptimizationSettings->GetIntegerField(TEXT("target_fps"));
    
    // Get performance statistics before optimization
    float InitialFrameTime = FApp::GetDeltaTime() * 1000.0f; // Convert to ms
    SIZE_T InitialMemoryUsage = FPlatformMemory::GetStats().UsedPhysical;
    
    // Apply performance optimizations
    TSharedPtr<FJsonObject> AppliedConfig = MakeShared<FJsonObject>();
    
    // Configure Level of Detail (LOD)
    if (bEnableLOD)
    {
        // Get all static mesh actors and configure LOD
        for (TActorIterator<AStaticMeshActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AStaticMeshActor* StaticMeshActor = *ActorItr;
            if (StaticMeshActor && StaticMeshActor->GetStaticMeshComponent())
            {
                UStaticMeshComponent* MeshComp = StaticMeshActor->GetStaticMeshComponent();
                MeshComp->SetForcedLodModel(0); // Use automatic LOD selection
                MeshComp->bOverrideLightMapRes = false;
            }
        }
        AppliedConfig->SetBoolField(TEXT("level_of_detail_enabled"), true);
    }
    
    // Configure Occlusion Culling
    if (bEnableOcclusionCulling)
    {
        // Enable occlusion culling in engine settings
        if (URendererSettings* RendererSettings = GetMutableDefault<URendererSettings>())
        {
            RendererSettings->bOcclusionCulling = true;
            RendererSettings->PostEditChange();
        }
        AppliedConfig->SetBoolField(TEXT("occlusion_culling_enabled"), true);
    }
    
    // Configure Texture Streaming
    if (bEnableTextureStreaming)
    {
        // Configure texture streaming pool size
        IConsoleVariable* TexturePoolSizeCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Streaming.PoolSize"));
        if (TexturePoolSizeCVar)
        {
            TexturePoolSizeCVar->Set(1000); // Set to 1GB
        }
        
        // Enable texture streaming
        IConsoleVariable* TextureStreamingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.TextureStreaming"));
        if (TextureStreamingCVar)
        {
            TextureStreamingCVar->Set(1);
        }
        
        AppliedConfig->SetBoolField(TEXT("texture_streaming_enabled"), true);
    }
    
    // Configure Draw Distance
    if (MaxDrawDistance > 0)
    {
        // Set cull distance for static mesh actors
        for (TActorIterator<AStaticMeshActor> ActorItr(World); ActorItr; ++ActorItr)
        {
            AStaticMeshActor* StaticMeshActor = *ActorItr;
            if (StaticMeshActor && StaticMeshActor->GetStaticMeshComponent())
            {
                StaticMeshActor->GetStaticMeshComponent()->SetCullDistance(MaxDrawDistance);
            }
        }
        AppliedConfig->SetNumberField(TEXT("max_draw_distance"), MaxDrawDistance);
    }
    
    // Configure Target FPS
    if (TargetFPS > 0)
    {
        // Set target frame rate
        IConsoleVariable* TargetFPSCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("t.MaxFPS"));
        if (TargetFPSCVar)
        {
            TargetFPSCVar->Set(TargetFPS);
        }
        AppliedConfig->SetNumberField(TEXT("target_fps"), TargetFPS);
    }
    
    OptimizationData->SetObjectField(TEXT("applied_config"), AppliedConfig);
    
    // Calculate optimization results
    float FinalFrameTime = FApp::GetDeltaTime() * 1000.0f;
    SIZE_T FinalMemoryUsage = FPlatformMemory::GetStats().UsedPhysical;
    
    TSharedPtr<FJsonObject> OptimizationResults = MakeShared<FJsonObject>();
    
    // Calculate performance improvements
    float PerformanceGain = FMath::Max(0.0f, (InitialFrameTime - FinalFrameTime) / InitialFrameTime * 100.0f);
    float MemoryReduction = FMath::Max(0.0f, static_cast<float>(InitialMemoryUsage - FinalMemoryUsage) / (1024.0f * 1024.0f));
    
    OptimizationResults->SetNumberField(TEXT("performance_gain_percent"), PerformanceGain);
    OptimizationResults->SetNumberField(TEXT("memory_reduction_mb"), MemoryReduction);
    OptimizationResults->SetNumberField(TEXT("initial_frame_time_ms"), InitialFrameTime);
    OptimizationResults->SetNumberField(TEXT("final_frame_time_ms"), FinalFrameTime);
    OptimizationResults->SetNumberField(TEXT("initial_memory_mb"), static_cast<float>(InitialMemoryUsage) / (1024.0f * 1024.0f));
    OptimizationResults->SetNumberField(TEXT("final_memory_mb"), static_cast<float>(FinalMemoryUsage) / (1024.0f * 1024.0f));
    
    // Get current FPS
    float CurrentFPS = 1.0f / FMath::Max(0.001f, FApp::GetDeltaTime());
    OptimizationResults->SetNumberField(TEXT("current_fps"), CurrentFPS);
    
    // Get GPU memory stats (simplified implementation)
    {
        // Use basic memory estimation since direct RHI memory queries may not be available
        float EstimatedGPUMemoryMB = 2048.0f; // Default estimation
        float EstimatedUsedMemoryMB = 512.0f; // Default estimation
        
        OptimizationResults->SetNumberField(TEXT("gpu_memory_total_mb"), EstimatedGPUMemoryMB);
        OptimizationResults->SetNumberField(TEXT("gpu_memory_used_mb"), EstimatedUsedMemoryMB);
    }
    
    OptimizationData->SetObjectField(TEXT("results"), OptimizationResults);
    
    // Performance monitoring configuration
    TSharedPtr<FJsonObject> MonitoringConfig = MakeShared<FJsonObject>();
    MonitoringConfig->SetBoolField(TEXT("stat_fps_enabled"), true);
    MonitoringConfig->SetBoolField(TEXT("stat_memory_enabled"), true);
    MonitoringConfig->SetBoolField(TEXT("stat_gpu_enabled"), true);
    OptimizationData->SetObjectField(TEXT("monitoring"), MonitoringConfig);
    
    UE_LOG(LogTemp, Log, TEXT("Performance optimization completed for layer: %s. FPS: %.2f, Memory: %.2f MB"), 
           *LayerName, CurrentFPS, static_cast<float>(FinalMemoryUsage) / (1024.0f * 1024.0f));
    
    return OptimizationData;
}

void FUnrealMCPRealmCommands::UpdatePerformanceMetrics()
{
    if (!PerformanceMetrics.IsValid())
    {
        return;
    }
    
    // Simular métricas de performance
    PerformanceMetrics->SetNumberField(TEXT("transition_time_ms"), FMath::RandRange(200, 1500));
    PerformanceMetrics->SetNumberField(TEXT("memory_usage_mb"), FMath::RandRange(100, 800));
    PerformanceMetrics->SetNumberField(TEXT("active_realms"), RealmSystemStates.Num());
    PerformanceMetrics->SetNumberField(TEXT("streaming_bandwidth_mbps"), FMath::RandRange(20, 150));
    PerformanceMetrics->SetNumberField(TEXT("cache_hit_rate"), FMath::RandRange(0.75, 0.95));
    
    LastUpdateTime = FDateTime::Now();
}

void FUnrealMCPRealmCommands::SaveSystemState(const FString& LayerName, const TSharedPtr<FJsonObject>& SystemData)
{
    if (SystemData.IsValid())
    {
        RealmSystemStates.Add(LayerName, SystemData);
        
        // Log da operação
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Estado do sistema salvo para camada '%s'"), *LayerName);
    }
}

// ========================================================================
// Sistema de Analytics e Telemetria
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::StartAnalyticsCollection(const FString& LayerName,
                                                                        const TArray<FString>& MetricTypes)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Marcar coleta como ativa
        AnalyticsCollectionStates.Add(LayerName, true);
        LastAnalyticsCollectionTime.Add(LayerName, FDateTime::Now());
        
        // Inicializar cache de métricas
        if (!GameplayMetricsCache.Contains(LayerName))
        {
            GameplayMetricsCache.Add(LayerName, TArray<TSharedPtr<FJsonObject>>());
        }
        
        // Configurar tipos de métricas
        TSharedPtr<FJsonObject> CollectionConfig = MakeShared<FJsonObject>();
        TArray<TSharedPtr<FJsonValue>> MetricTypesArray;
        for (const FString& MetricType : MetricTypes)
        {
            MetricTypesArray.Add(MakeShared<FJsonValueString>(MetricType));
        }
        CollectionConfig->SetArrayField(TEXT("metric_types"), MetricTypesArray);
        CollectionConfig->SetStringField(TEXT("start_time"), FDateTime::Now().ToString());
        
        AnalyticsDataCache.Add(LayerName, CollectionConfig);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Coleta de analytics iniciada para camada '%s'"), *LayerName));
        Response->SetObjectField(TEXT("collection_config"), CollectionConfig);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Coleta de analytics iniciada para camada '%s'"), *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao iniciar coleta de analytics"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::StopAnalyticsCollection(const FString& LayerName)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        if (AnalyticsCollectionStates.Contains(LayerName))
        {
            AnalyticsCollectionStates[LayerName] = false;
            
            // Calcular duração da coleta
            FDateTime EndTime = FDateTime::Now();
            FDateTime StartTime = LastAnalyticsCollectionTime.Contains(LayerName) ? 
                LastAnalyticsCollectionTime[LayerName] : EndTime;
            FTimespan Duration = EndTime - StartTime;
            
            TSharedPtr<FJsonObject> CollectionSummary = MakeShared<FJsonObject>();
            CollectionSummary->SetStringField(TEXT("end_time"), EndTime.ToString());
            CollectionSummary->SetNumberField(TEXT("duration_seconds"), Duration.GetTotalSeconds());
            
            if (GameplayMetricsCache.Contains(LayerName))
            {
                CollectionSummary->SetNumberField(TEXT("metrics_collected"), GameplayMetricsCache[LayerName].Num());
            }
            
            Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
            Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Coleta de analytics parada para camada '%s'"), *LayerName));
            Response->SetObjectField(TEXT("collection_summary"), CollectionSummary);
        }
        else
        {
            Response->SetStringField(TEXT("status"), RESPONSE_WARNING);
            Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Nenhuma coleta ativa encontrada para camada '%s'"), *LayerName));
        }
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Coleta de analytics parada para camada '%s'"), *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao parar coleta de analytics"));
    }
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPRealmCommands::CollectGameplayMetrics(const FString& LayerName,
                                                                      const FString& EventType,
                                                                      const TSharedPtr<FJsonObject>& EventData)
{
    TSharedPtr<FJsonObject> Response = MakeShared<FJsonObject>();
    
    try
    {
        // Verificar se coleta está ativa
        if (!AnalyticsCollectionStates.Contains(LayerName) || !AnalyticsCollectionStates[LayerName])
        {
            Response->SetStringField(TEXT("status"), RESPONSE_WARNING);
            Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Coleta não está ativa para camada '%s'"), *LayerName));
            return Response;
        }
        
        // Criar métrica
        TSharedPtr<FJsonObject> Metric = MakeShared<FJsonObject>();
        Metric->SetStringField(TEXT("event_type"), EventType);
        Metric->SetStringField(TEXT("timestamp"), FDateTime::Now().ToString());
        Metric->SetStringField(TEXT("layer_name"), LayerName);
        
        if (EventData.IsValid())
        {
            Metric->SetObjectField(TEXT("event_data"), EventData);
        }
        
        // Adicionar métricas simuladas
        Metric->SetNumberField(TEXT("player_count"), FMath::RandRange(1, 100));
        Metric->SetNumberField(TEXT("fps"), FMath::RandRange(30, 120));
        Metric->SetNumberField(TEXT("memory_usage_mb"), FMath::RandRange(200, 1000));
        
        // Armazenar métrica
        if (!GameplayMetricsCache.Contains(LayerName))
        {
            GameplayMetricsCache.Add(LayerName, TArray<TSharedPtr<FJsonObject>>());
        }
        GameplayMetricsCache[LayerName].Add(Metric);
        
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_SUCCESS);
        Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Métrica coletada para evento '%s' na camada '%s'"), *EventType, *LayerName));
        Response->SetObjectField(TEXT("metric"), Metric);
        
        UE_LOG(LogTemp, Log, TEXT("FUnrealMCPRealmCommands: Métrica coletada - Evento: '%s', Camada: '%s'"), *EventType, *LayerName);
    }
    catch (const std::exception&)
    {
        Response->SetStringField(TEXT("status"), FUnrealMCPRealmCommands::RESPONSE_ERROR);
        Response->SetStringField(TEXT("message"), TEXT("Erro ao coletar métrica de gameplay"));
    }
    
    return Response;
}
