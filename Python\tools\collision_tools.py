"""
Collision Tools for Unreal MCP.

This module provides tools for creating and manipulating collision systems in Unreal Engine.
Implements Multi-Layer Collision System tools for MCP server integration.

Based on Technical Architecture Document:
- Custom collision channels for different layers
- Configurable collision sizes and interactions
- Layer-specific collision management
- Performance optimization for collision detection
"""

import logging
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

def register_collision_tools(mcp: FastMCP):
    """Register collision tools with the MCP server."""

    @mcp.tool()
    def create_collision_channel(
        ctx: Context,
        channel_name: str,
        channel_type: str = "ECR_Block",
        description: str = ""
    ) -> Dict[str, Any]:
        """Create a custom collision channel for multi-layer system."""
        # Import inside function to avoid circular imports
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_collision_channel", {
                "channel_name": channel_name,
                "channel_type": channel_type,
                "description": description
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision channel creation response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating collision channel: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_collision_profile(
        ctx: Context,
        profile_name: str,
        collision_settings: Dict[str, str]
    ) -> Dict[str, Any]:
        """Configure a collision profile with specific channel responses."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_collision_profile", {
                "profile_name": profile_name,
                "collision_settings": collision_settings
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision profile configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring collision profile: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def set_layer_collision_rules(
        ctx: Context,
        layer_name: str,
        collision_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Set collision rules for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("set_layer_collision_rules", {
                "layer_name": layer_name,
                "collision_rules": collision_rules
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision rules response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting layer collision rules: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def configure_collision_size_scaling(
        ctx: Context,
        layer_name: str,
        scale_factor: float,
        size_rules: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure collision size scaling for a layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_collision_size_scaling", {
                "layer_name": layer_name,
                "scale_factor": scale_factor,
                "size_rules": size_rules
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision size scaling response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring collision size scaling: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def create_layer_collision_matrix(
        ctx: Context,
        layers: List[str],
        interaction_matrix: Dict[str, Dict[str, str]]
    ) -> Dict[str, Any]:
        """Create a collision interaction matrix between layers."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("create_layer_collision_matrix", {
                "layers": layers,
                "interaction_matrix": interaction_matrix
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision matrix response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error creating layer collision matrix: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}

    @mcp.tool()
    def optimize_collision_detection(ctx: Context, layer_name: str, optimization_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Optimize collision detection for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("optimize_collision_detection", {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision optimization response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error optimizing collision detection: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def get_collision_system_status(ctx: Context) -> Dict[str, Any]:
        """Get current status of the collision system."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("get_collision_system_status", {})
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision system status response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error getting collision system status: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_collision_complexity(ctx: Context, actor_name: str, complexity_type: str, custom_settings: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Configure collision complexity for an actor."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("configure_collision_complexity", {
                "actor_name": actor_name,
                "complexity_type": complexity_type,
                "custom_settings": custom_settings or {}
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Collision complexity configuration response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error configuring collision complexity: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_layer_collision_filtering(ctx: Context, layer_name: str, filter_settings: Dict[str, Any]) -> Dict[str, Any]:
        """Setup collision filtering for a specific layer."""
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
                
            response = unreal.send_command("setup_layer_collision_filtering", {
                "layer_name": layer_name,
                "filter_settings": filter_settings
            })
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Layer collision filtering response: {response}")
            return response or {}
            
        except Exception as e:
            error_msg = f"Error setting up layer collision filtering: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}