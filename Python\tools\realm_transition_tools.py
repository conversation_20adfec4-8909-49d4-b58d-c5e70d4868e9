#!/usr/bin/env python3
"""
Realm Transition Tools for Unreal MCP.

This module provides tools for creating and managing realm transition systems in Unreal Engine.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from mcp.server.fastmcp import FastMCP, Context

# Get logger
logger = logging.getLogger("UnrealMCP")

class TransitionType(Enum):
    """Tipos de transição entre realms"""
    SEAMLESS = "seamless"
    LOADING_SCREEN = "loading_screen"
    FADE = "fade"
    PORTAL = "portal"
    TELEPORT = "teleport"

class StreamingStrategy(Enum):
    """Estratégias de streaming de assets"""
    PRELOAD_ALL = "preload_all"
    LAZY_LOADING = "lazy_loading"
    PREDICTIVE = "predictive"
    DISTANCE_BASED = "distance_based"
    PRIORITY_BASED = "priority_based"

class PartitioningMethod(Enum):
    """Métodos de particionamento de mundo"""
    GRID_BASED = "grid_based"
    HIERARCHICAL = "hierarchical"
    ADAPTIVE = "adaptive"
    CONTENT_AWARE = "content_aware"
    PERFORMANCE_BASED = "performance_based"

@dataclass
class RealmConfig:
    """Configuração de um realm"""
    realm_id: str
    realm_name: str
    world_size: Tuple[float, float, float]
    max_players: int
    streaming_strategy: StreamingStrategy
    partitioning_method: PartitioningMethod
    transition_points: List[Dict[str, Any]]
    asset_bundles: List[str]
    performance_targets: Dict[str, float]

@dataclass
class TransitionConfig:
    """Configuração de transição entre realms"""
    transition_id: str
    source_realm: str
    target_realm: str
    transition_type: TransitionType
    trigger_conditions: Dict[str, Any]
    preload_assets: List[str]
    cleanup_assets: List[str]
    transition_duration: float
    validation_rules: List[str]

def register_realm_transition_tools(mcp: FastMCP):
    """Register Realm Transition tools with the MCP server."""
    
    @mcp.tool()
    def create_realm_transition_system(
        ctx: Context,
        layer_name: str, 
        realm_configs: List[Dict[str, Any]],
        transition_configs: List[Dict[str, Any]],
        global_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Cria um sistema de transição de realms multicamada.
        
        Args:
            layer_name: Nome da camada do sistema
            realm_configs: Lista de configurações de realms
            transition_configs: Lista de configurações de transições
            global_settings: Configurações globais do sistema
        
        Returns:
            Dicionário com informações do sistema criado
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "realm_configs": realm_configs,
                "transition_configs": transition_configs,
                "global_settings": global_settings or {}
            }
            
            logger.info(f"Creating realm transition system for layer: {layer_name}")
            response = unreal.send_command("create_realm_transition_system", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm transition system creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating realm transition system: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_asset_streaming(
        ctx: Context,
        layer_name: str,
        streaming_strategy: str,
        asset_priorities: Dict[str, int],
        bandwidth_limits: Dict[str, float],
        cache_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Configura o sistema de streaming de assets para transições de realms.
        
        Args:
            layer_name: Nome da camada
            streaming_strategy: Estratégia de streaming
            asset_priorities: Prioridades dos assets
            bandwidth_limits: Limites de largura de banda
            cache_settings: Configurações de cache
        
        Returns:
            Dicionário com configuração do streaming
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "strategy": streaming_strategy,
                "asset_priorities": asset_priorities,
                "bandwidth_limits": bandwidth_limits,
                "cache_settings": cache_settings or {
                    "max_cache_size_mb": 2048,
                    "cache_eviction_policy": "lru",
                    "preload_threshold": 0.8
                }
            }
            
            logger.info(f"Configuring asset streaming for layer: {layer_name}")
            response = unreal.send_command("configure_asset_streaming", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Asset streaming configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring asset streaming: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_world_partitioning(
        ctx: Context,
        layer_name: str,
        method: str,
        partition_size: Tuple[float, float, float],
        overlap_distance: float = 1000.0
    ) -> Dict[str, Any]:
        """
        Configura o sistema de particionamento de mundo para realms.
        
        Args:
            layer_name: Nome da camada
            method: Método de particionamento (grid_based, hierarchical, adaptive, content_aware, performance_based)
            partition_size: Tamanho das partições como [X, Y, Z]
            overlap_distance: Distância de sobreposição (padrão: 1000.0)
        
        Returns:
            Dicionário com configuração do particionamento
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "method": method,
                "partition_size": list(partition_size),
                "overlap_distance": overlap_distance
            }
            
            logger.info(f"Setting up world partitioning for layer: {layer_name}")
            response = unreal.send_command("setup_world_partitioning", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"World partitioning setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up world partitioning: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def create_transition_triggers(
        ctx: Context,
        layer_name: str,
        trigger_configs: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Cria triggers para transições entre realms.
        
        Args:
            layer_name: Nome da camada
            trigger_configs: Configurações dos triggers
        
        Returns:
            Dicionário com informações dos triggers criados
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "trigger_configs": trigger_configs
            }
            
            logger.info(f"Creating transition triggers for layer: {layer_name}")
            response = unreal.send_command("create_transition_triggers", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Transition triggers creation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error creating transition triggers: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def configure_realm_persistence(
        ctx: Context,
        layer_name: str,
        persistence_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configura a persistência de estado dos realms.
        
        Args:
            layer_name: Nome da camada
            persistence_settings: Configurações de persistência
        
        Returns:
            Dicionário com configuração de persistência
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "persistence_settings": persistence_settings
            }
            
            logger.info(f"Configuring realm persistence for layer: {layer_name}")
            response = unreal.send_command("configure_realm_persistence", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm persistence configuration response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error configuring realm persistence: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_cross_realm_communication(
        ctx: Context,
        layer_name: str,
        channels: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Configura comunicação entre realms.
        
        Args:
            layer_name: Nome da camada
            channels: Canais de comunicação
        
        Returns:
            Dicionário com configuração de comunicação
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "channels": channels
            }
            
            logger.info(f"Setting up cross-realm communication for layer: {layer_name}")
            response = unreal.send_command("setup_cross_realm_communication", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Cross-realm communication setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up cross-realm communication: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def optimize_transition_performance(
        ctx: Context,
        layer_name: str,
        optimization_settings: Dict[str, Any],
        performance_targets: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        Otimiza a performance das transições entre realms.
        
        Args:
            layer_name: Nome da camada
            optimization_settings: Configurações de otimização
            performance_targets: Metas de performance
        
        Returns:
            Dicionário com resultados da otimização
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "optimization_settings": optimization_settings,
                "performance_targets": performance_targets
            }
            
            logger.info(f"Optimizing transition performance for layer: {layer_name}")
            response = unreal.send_command("optimize_transition_performance", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance optimization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error optimizing transition performance: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def debug_realm_transitions(
        ctx: Context,
        layer_name: str,
        debug_level: str = "info",
        capture_metrics: bool = True
    ) -> Dict[str, Any]:
        """
        Ativa debugging para transições de realm.
        
        Args:
            layer_name: Nome da camada
            debug_level: Nível de debug (info, warning, error)
            capture_metrics: Se deve capturar métricas
        
        Returns:
            Dicionário com informações de debug
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "debug_level": debug_level,
                "capture_metrics": capture_metrics
            }
            
            logger.info(f"Starting realm transition debugging for layer: {layer_name}")
            response = unreal.send_command("debug_realm_transitions", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Debug realm transitions response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error debugging realm transitions: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def validate_realm_setup(
        ctx: Context,
        layer_name: str,
        validation_rules: List[str],
        strict_mode: bool = False
    ) -> Dict[str, Any]:
        """
        Valida a configuração de um realm.
        
        Args:
            layer_name: Nome da camada
            validation_rules: Regras de validação
            strict_mode: Modo estrito de validação
        
        Returns:
            Dicionário com resultados da validação
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "validation_rules": validation_rules,
                "strict_mode": strict_mode
            }
            
            logger.info(f"Validating realm setup for layer: {layer_name}")
            response = unreal.send_command("validate_realm_setup", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Realm validation response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error validating realm setup: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def start_analytics_collection(
        ctx: Context,
        layer_name: str,
        metrics_config: Dict[str, Any],
        collection_interval: float = 1.0
    ) -> Dict[str, Any]:
        """
        Inicia coleta de analytics para realm transitions.
        
        Args:
            layer_name: Nome da camada
            metrics_config: Configuração das métricas
            collection_interval: Intervalo de coleta em segundos
        
        Returns:
            Dicionário com status da coleta
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "metrics_config": metrics_config,
                "collection_interval": collection_interval
            }
            
            logger.info(f"Starting analytics collection for layer: {layer_name}")
            response = unreal.send_command("start_analytics_collection", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics collection start response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error starting analytics collection: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def process_analytics_data(
        ctx: Context,
        layer_name: str,
        data_filters: Dict[str, Any],
        processing_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Processa dados de analytics coletados.
        
        Args:
            layer_name: Nome da camada
            data_filters: Filtros para os dados
            processing_options: Opções de processamento
        
        Returns:
            Dicionário com dados processados
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "data_filters": data_filters,
                "processing_options": processing_options
            }
            
            logger.info(f"Processing analytics data for layer: {layer_name}")
            response = unreal.send_command("process_analytics_data", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics data processing response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error processing analytics data: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def generate_analytics_visualization(
        ctx: Context,
        layer_name: str,
        visualization_type: str,
        data_range: Dict[str, Any],
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """
        Gera visualizações dos dados de analytics.
        
        Args:
            layer_name: Nome da camada
            visualization_type: Tipo de visualização
            data_range: Intervalo de dados
            export_format: Formato de exportação
        
        Returns:
            Dicionário com dados de visualização
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "visualization_type": visualization_type,
                "data_range": data_range,
                "export_format": export_format
            }
            
            logger.info(f"Generating analytics visualization for layer: {layer_name}")
            response = unreal.send_command("generate_analytics_visualization", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics visualization response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error generating analytics visualization: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def setup_performance_alerts(
        ctx: Context,
        layer_name: str,
        alert_thresholds: Dict[str, float],
        notification_settings: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Configura alertas de performance para realm transitions.
        
        Args:
            layer_name: Nome da camada
            alert_thresholds: Limites para alertas
            notification_settings: Configurações de notificação
        
        Returns:
            Dicionário com configuração de alertas
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "alert_thresholds": alert_thresholds,
                "notification_settings": notification_settings
            }
            
            logger.info(f"Setting up performance alerts for layer: {layer_name}")
            response = unreal.send_command("setup_performance_alerts", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Performance alerts setup response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error setting up performance alerts: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    @mcp.tool()
    def export_analytics_report(
        ctx: Context,
        layer_name: str,
        report_type: str,
        date_range: Dict[str, str],
        export_path: str
    ) -> Dict[str, Any]:
        """
        Exporta relatório de analytics.
        
        Args:
            layer_name: Nome da camada
            report_type: Tipo de relatório
            date_range: Intervalo de datas
            export_path: Caminho para exportação
        
        Returns:
            Dicionário com informações da exportação
        """
        from unreal_mcp_server import get_unreal_connection
        
        try:
            unreal = get_unreal_connection()
            if not unreal:
                logger.error("Failed to connect to Unreal Engine")
                return {"success": False, "message": "Failed to connect to Unreal Engine"}
            
            params = {
                "layer_name": layer_name,
                "report_type": report_type,
                "date_range": date_range,
                "export_path": export_path
            }
            
            logger.info(f"Exporting analytics report for layer: {layer_name}")
            response = unreal.send_command("export_analytics_report", params)
            
            if not response:
                logger.error("No response from Unreal Engine")
                return {"success": False, "message": "No response from Unreal Engine"}
            
            logger.info(f"Analytics report export response: {response}")
            return response
            
        except Exception as e:
            error_msg = f"Error exporting analytics report: {e}"
            logger.error(error_msg)
            return {"success": False, "message": error_msg}
    
    logger.info("Realm transition tools registered successfully")