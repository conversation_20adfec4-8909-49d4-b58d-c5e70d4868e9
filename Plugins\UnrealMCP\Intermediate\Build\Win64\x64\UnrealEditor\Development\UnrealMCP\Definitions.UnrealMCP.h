// Generated by UnrealBuildTool (UEBuildModuleCPP.cs) : Shared PCH Definitions for UnrealMCP
#pragma once
#include "SharedDefinitions.UnrealEd.Project.ValApi.ValExpApi.Cpp20.h"
#undef UNREALMCP_API
#define UE_IS_ENGINE_MODULE 0
#define UE_DEPRECATED_FORGAME UE_DEPRECATED
#define UE_DEPRECATED_FORENGINE UE_DEPRECATED
#define UE_VALIDATE_FORMAT_STRINGS 1
#define UE_VALIDATE_INTERNAL_API 1
#define UE_VALIDATE_EXPERIMENTAL_API 1
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_2 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_3 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_4 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_5 0
#define UE_ENABLE_INCLUDE_ORDER_DEPRECATED_IN_5_6 0
#define UE_PROJECT_NAME Auracron
#define UE_TARGET_NAME AuracronEditor
#define UE_MODULE_NAME "UnrealMCP"
#define UE_PLUGIN_NAME "UnrealMCP"
#define IMPLEMENT_ENCRYPTION_KEY_REGISTRATION() 
#define IMPLEMENT_SIGNING_KEY_REGISTRATION() 
#define EDITORSCRIPTINGUTILITIES_API DLLIMPORT
#define STATICMESHEDITOR_API DLLIMPORT
#define BLUEPRINTEDITORLIBRARY_API DLLIMPORT
#define UMGEDITOR_API DLLIMPORT
#define SEQUENCERCORE_API DLLIMPORT
#define CURVEEDITOR_API DLLIMPORT
#define SEQUENCERWIDGETS_API DLLIMPORT
#define SEQUENCER_API DLLIMPORT
#define SCENEOUTLINER_API DLLIMPORT
#define UNREALMCP_API DLLEXPORT
#define NIAGARA_API DLLIMPORT
#define NIAGARACORE_API DLLIMPORT
#define VECTORVM_API DLLIMPORT
#define NIAGARASHADER_API DLLIMPORT
#define NIAGARAVERTEXFACTORIES_API DLLIMPORT
#define FOLIAGE_API DLLIMPORT
#define WITH_GAMEPLAY_DEBUGGER_CORE 1
#define WITH_GAMEPLAY_DEBUGGER 1
#define WITH_GAMEPLAY_DEBUGGER_MENU 1
#define AIMODULE_API DLLIMPORT
#define MASSENTITY_API DLLIMPORT
#define MASSMOVEMENT_API DLLIMPORT
#define MASSCOMMON_API DLLIMPORT
#define MASSLOD_API DLLIMPORT
#define MASSSIMULATION_API DLLIMPORT
#define MASSSPAWNER_API DLLIMPORT
#define ZONEGRAPH_API DLLIMPORT
#define MASSENTITYEDITOR_API DLLIMPORT
#define COMPONENTVISUALIZERS_API DLLIMPORT
#define MASSSIGNALS_API DLLIMPORT
#define ZONEGRAPHANNOTATIONS_API DLLIMPORT
#define ZONEGRAPHDEBUG_API DLLIMPORT
#define MASSREPRESENTATION_API DLLIMPORT
#define MASSACTORS_API DLLIMPORT
#define MASSREPLICATION_API DLLIMPORT
#define WITH_STATETREE_TRACE 1
#define WITH_STATETREE_TRACE_DEBUGGER 1
#define STATETREEMODULE_API DLLIMPORT
#define PROPERTYBINDINGUTILS_API DLLIMPORT
#define TRACESERVICES_API DLLIMPORT
#define CBOR_API DLLIMPORT
#define TRACEANALYSIS_API DLLIMPORT
#define GAMEPLAYABILITIES_API DLLIMPORT
#define DATAREGISTRY_API DLLIMPORT
#define PCG_API DLLIMPORT
#define COMPUTEFRAMEWORK_API DLLIMPORT
#define EDITORSTYLE_API DLLIMPORT
